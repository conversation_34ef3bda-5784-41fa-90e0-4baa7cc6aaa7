{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751530857868}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 步骤指示器 -->\n    <div class=\"steps-container\">\n      <!-- 左侧占位区域，保持布局平衡 -->\n      <div class=\"left-placeholder\"></div>\n\n      <!-- 步骤指示器 -->\n      <div class=\"steps-wrapper\">\n        <div class=\"step-item\" :class=\"{ active: currentStep === 1 }\">\n          <span class=\"step-number\">1</span>\n          <span class=\"step-text\">舆情分析来源</span>\n        </div>\n        <div class=\"step-item\" :class=\"{ active: currentStep === 2 }\">\n          <span class=\"step-number\">2</span>\n          <span class=\"step-text\">数据概览</span>\n        </div>\n      </div>\n\n      <!-- 右侧按钮区域 -->\n      <div class=\"right-actions\">\n        <el-button\n          class=\"timed-push-btn\"\n          type=\"primary\"\n          size=\"small\"\n          @click=\"handleTimedPush\"\n        >\n          定时推送\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 第一步：分析需求 -->\n      <div v-if=\"currentStep === 1\" class=\"analysis-source\">\n        <h2 class=\"section-title\">分析需求</h2>\n\n        <!-- 实体关键词区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            实体关键词\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"entityKeyword\"\n            placeholder=\"请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等\"\n            class=\"entity-input\"\n            :class=\"{ 'error': !entityKeyword.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 具体需求区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            具体需求\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"specificRequirement\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）\"\n            class=\"requirement-textarea\"\n            :class=\"{ 'error': !specificRequirement.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 选择关联词区域 -->\n        <div class=\"related-words-section\">\n          <div class=\"section-header\">\n            <div class=\"header-left\">\n              <span class=\"section-label\">选择关联词</span>\n              <span class=\"word-count\" :class=\"{ 'max-reached': selectedKeywords.length >= maxKeywords }\">\n                ({{ selectedKeywords.length }}/{{ maxKeywords }})\n              </span>\n            </div>\n            <el-button\n              v-if=\"generatedKeywords.length > 0\"\n              class=\"regenerate-btn\"\n              size=\"mini\"\n              type=\"text\"\n              @click=\"regenerateKeywords\"\n            >\n              <i class=\"el-icon-refresh\"></i>\n              重新生成\n            </el-button>\n          </div>\n\n          <div class=\"keywords-textbox-wrapper\">\n            <!-- 显示生成的关键词 -->\n            <div v-if=\"generatedKeywords.length > 0\" class=\"generated-keywords-display\">\n              <div v-for=\"(category, categoryName) in groupedKeywords\" :key=\"categoryName\" class=\"keyword-category\">\n                <el-button\n                  class=\"category-button\"\n                  size=\"small\"\n                  type=\"primary\"\n                  plain\n                  @click=\"toggleCategorySelection(categoryName, category)\"\n                >\n                  {{ categoryName }}\n                </el-button>\n                <div class=\"keyword-tags\">\n                  <el-tag\n                    v-for=\"(keyword, index) in category\"\n                    :key=\"index\"\n                    :class=\"['keyword-tag', { selected: isKeywordSelected(keyword) }]\"\n                    @click=\"toggleKeyword(keyword)\"\n                  >\n                    {{ keyword }}\n                  </el-tag>\n                </div>\n              </div>\n            </div>\n\n            <!-- 生成关联词按钮区域 -->\n            <div v-if=\"generatedKeywords.length === 0\" class=\"words-container\">\n              <div class=\"generate-word-btn\" @click=\"generateRelatedWords\">\n                <i class=\"el-icon-magic-stick\"></i>\n                <span>生成关联词</span>\n              </div>\n              <div class=\"word-description\">\n                根据你填写的需求和关键词生成关联词\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第二步：数据概览 -->\n      <div v-if=\"currentStep === 2\" class=\"data-overview\">\n        <h2 class=\"section-title\">选择数据来源</h2>\n\n        <!-- 数据来源选项 -->\n        <div class=\"data-source-section\">\n          <div class=\"source-option\" @click=\"toggleDataSource('online-search')\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"'online-search'\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-search\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>联网搜索</h3>\n            </div>\n          </div>\n\n          <!-- 自定义数据源列表 -->\n          <div v-for=\"(source, index) in customDataSources\" :key=\"index\" class=\"source-option\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"source\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-link\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>{{ source }}</h3>\n            </div>\n            <div class=\"source-actions\">\n              <i class=\"el-icon-delete\" @click=\"removeCustomSource(index)\"></i>\n            </div>\n          </div>\n\n          <!-- 新增数据源表单 -->\n          <div v-if=\"showAddSourceInput\" class=\"add-source-form\">\n            <div class=\"form-header\">\n              <h3>新增数据源</h3>\n              <i class=\"el-icon-close\" @click=\"hideAddSourceForm\"></i>\n            </div>\n            <div class=\"form-item\">\n              <label class=\"form-label\">\n                数据源网址\n                <span class=\"required\">*</span>\n              </label>\n              <div class=\"input-group\">\n                <el-input\n                  v-model=\"newSourceUrl\"\n                  placeholder=\"请输入网址，例如：https://www.example.com\"\n                  class=\"source-url-input\"\n                  @keyup.enter=\"confirmAddSource\"\n                />\n                <el-button type=\"primary\" @click=\"confirmAddSource\">确定</el-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- 新增来源按钮 -->\n          <div v-if=\"!showAddSourceInput\" class=\"add-source-btn\" @click=\"showAddSourceForm\">\n            <i class=\"el-icon-plus\"></i>\n            <span>新增来源</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 底部按钮区域 -->\n      <div class=\"bottom-actions\">\n        <el-button v-if=\"currentStep === 2\" @click=\"goToPreviousStep\" size=\"large\">上一步</el-button>\n        <el-button\n          v-if=\"currentStep === 1\"\n          @click=\"goToNextStep\"\n          type=\"primary\"\n          size=\"large\"\n          :disabled=\"!canGoToNextStep\"\n        >下一步</el-button>\n        <el-button v-if=\"currentStep === 2\" type=\"primary\" size=\"large\">开始分析</el-button>\n      </div>\n    </div>\n\n    <!-- 定时任务抽屉 -->\n    <el-drawer\n      title=\"定时任务\"\n      :visible.sync=\"timedTaskDialogVisible\"\n      direction=\"rtl\"\n      size=\"600px\"\n      :before-close=\"closeTimedTaskDialog\"\n      custom-class=\"timed-task-drawer\"\n    >\n      <!-- 抽屉头部右侧按钮 -->\n      <div slot=\"title\" class=\"drawer-header\">\n        <span class=\"drawer-title\">定时任务</span>\n        <el-button\n          type=\"primary\"\n          size=\"mini\"\n          icon=\"el-icon-plus\"\n          @click=\"handleAddTimedTask\"\n          class=\"add-task-btn\"\n        >\n          定时任务\n        </el-button>\n      </div>\n\n      <!-- 抽屉内容 -->\n      <div class=\"drawer-content\">\n        <!-- 空状态 -->\n        <div v-if=\"timedTaskList.length === 0\" class=\"empty-state\">\n          <div class=\"empty-content\">\n            <!-- 空状态图标 -->\n            <div class=\"empty-icon\">\n              <svg width=\"120\" height=\"120\" viewBox=\"0 0 120 120\" fill=\"none\">\n                <!-- 文件夹图标 -->\n                <path d=\"M20 30h25l5-10h50v70H20V30z\" fill=\"#f0f0f0\" stroke=\"#d0d0d0\" stroke-width=\"2\"/>\n                <path d=\"M25 35h70v50H25V35z\" fill=\"#fafafa\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <!-- 文档图标 -->\n                <rect x=\"35\" y=\"45\" width=\"30\" height=\"25\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\"/>\n                <rect x=\"70\" y=\"50\" width=\"20\" height=\"15\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\"/>\n                <!-- 装饰线条 -->\n                <line x1=\"40\" y1=\"52\" x2=\"60\" y2=\"52\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"40\" y1=\"57\" x2=\"55\" y2=\"57\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"40\" y1=\"62\" x2=\"58\" y2=\"62\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"75\" y1=\"55\" x2=\"85\" y2=\"55\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"75\" y1=\"60\" x2=\"82\" y2=\"60\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n              </svg>\n            </div>\n            <p class=\"empty-text\">暂无定时任务</p>\n            <el-button type=\"primary\" @click=\"handleCreateTimedTask\" class=\"create-btn\">\n              去创建\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 任务列表 -->\n        <div v-else class=\"task-list\">\n          <!-- TODO: 这里将来显示定时任务列表 -->\n        </div>\n      </div>\n\n      <!-- 创建任务弹窗 -->\n      <el-dialog\n        title=\"定时任务\"\n        :visible.sync=\"createTaskDialogVisible\"\n        width=\"500px\"\n        :before-close=\"closeCreateTaskDialog\"\n        :append-to-body=\"false\"\n        class=\"create-task-dialog\"\n      >\n        <div class=\"task-form\">\n          <!-- 任务需求 -->\n          <div class=\"task-requirement-section\">\n            <div class=\"section-label\">\n              任务需求\n              <span class=\"required\">*</span>\n            </div>\n            <div class=\"form-group\">\n              <div class=\"input-label\">任务名称</div>\n              <el-input\n                v-model=\"taskForm.name\"\n                placeholder=\"请输入任务名称\"\n                class=\"task-name-input\"\n              />\n            </div>\n            <div class=\"form-group\">\n              <div class=\"input-label\">任务描述</div>\n              <el-input\n                v-model=\"taskForm.description\"\n                type=\"textarea\"\n                :rows=\"3\"\n                placeholder=\"请描述具体的任务需求，例如：基于当前分析需求自动生成AI新闻总结\"\n                class=\"task-description-input\"\n              />\n            </div>\n          </div>\n\n          <!-- 执行时间 -->\n          <div class=\"execute-time-section\">\n            <div class=\"section-label\">执行时间</div>\n            <div class=\"time-selector\">\n              <el-select v-model=\"taskForm.frequency\" placeholder=\"选择频率\" class=\"frequency-select\">\n                <el-option label=\"每天\" value=\"daily\"></el-option>\n                <el-option label=\"每周\" value=\"weekly\"></el-option>\n                <el-option label=\"每月\" value=\"monthly\"></el-option>\n              </el-select>\n              <el-time-picker\n                v-model=\"taskForm.executeTime\"\n                format=\"HH:mm\"\n                value-format=\"HH:mm\"\n                placeholder=\"选择时间\"\n                class=\"time-picker\"\n              >\n              </el-time-picker>\n            </div>\n          </div>\n        </div>\n\n        <!-- 底部按钮 -->\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"modifyPlan\" class=\"modify-btn\">修改计划</el-button>\n          <el-button type=\"primary\" @click=\"saveAndRunTask\" class=\"run-btn\">保存并运行</el-button>\n          <el-button type=\"success\" @click=\"saveTaskPlan\" class=\"save-btn\">保存计划</el-button>\n        </div>\n      </el-dialog>\n    </el-drawer>\n\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      currentStep: 1, // 当前步骤\n      entityKeyword: '', // 实体关键词\n      specificRequirement: '', // 具体需求\n      selectedKeywords: [], // 已选择的关键词\n      generatedKeywords: [], // 生成的所有关键词\n      maxKeywords: 5, // 最大选择数量\n      selectedDataSources: ['online-search'], // 已选择的数据来源\n      customDataSources: [], // 自定义数据源列表\n      showAddSourceInput: false, // 显示新增数据源表单\n      newSourceUrl: '', // 新增数据源URL\n      showValidation: false, // 是否显示验证错误样式\n      timedTaskDialogVisible: false, // 定时任务抽屉显示状态\n      timedTaskList: [], // 定时任务列表\n      createTaskDialogVisible: false, // 创建任务弹窗显示状态\n      taskForm: {\n        name: '总结AI新闻',\n        description: '基于当前分析需求自动生成AI新闻总结',\n        executeTime: '16:00',\n        frequency: 'daily'\n      }\n    }\n  },\n  computed: {\n    // 检查是否可以进入下一步\n    canGoToNextStep() {\n      // 检查实体关键词是否填写\n      if (!this.entityKeyword.trim()) {\n        return false\n      }\n\n      // 检查具体需求是否填写\n      if (!this.specificRequirement.trim()) {\n        return false\n      }\n\n      // 检查是否至少选择了一个关键词\n      if (this.selectedKeywords.length === 0) {\n        return false\n      }\n\n      return true\n    },\n\n    // 将关键词按分类分组\n    groupedKeywords() {\n      if (this.generatedKeywords.length === 0) {\n        return {}\n      }\n\n      // 动态生成分类按钮\n      const categories = [\n        { name: '售后服务问题', keywords: [] },\n        { name: '产品质量问题', keywords: [] },\n        { name: '投诉处理结果', keywords: [] },\n        { name: '消费者不满', keywords: [] },\n        { name: '虚假宣传', keywords: [] }\n      ]\n\n      this.generatedKeywords.forEach(keyword => {\n        let assigned = false\n\n        categories.forEach(cat => {\n          if (cat.name === '售后服务问题' && (keyword.includes('售后') || keyword.includes('服务') || keyword.includes('客服'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '产品质量问题' && (keyword.includes('质量') || keyword.includes('爆炸') || keyword.includes('故障'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '投诉处理结果' && (keyword.includes('投诉') || keyword.includes('处理') || keyword.includes('对解'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '消费者不满' && (keyword.includes('不满') || keyword.includes('消费者'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '虚假宣传' && (keyword.includes('宣传') || keyword.includes('充好'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          }\n        })\n\n        if (!assigned) {\n          // 如果没有匹配的分类，添加到第一个有关键词的分类或创建新分类\n          if (categories[0].keywords.length === 0) {\n            categories[0].keywords.push(keyword)\n          } else {\n            categories.find(cat => cat.keywords.length > 0).keywords.push(keyword)\n          }\n        }\n      })\n\n      // 只返回有关键词的分类\n      const result = {}\n      categories.forEach(cat => {\n        if (cat.keywords.length > 0) {\n          result[cat.name] = cat.keywords\n        }\n      })\n\n      return result\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 切换关键词选择状态\n    toggleKeyword(keyword) {\n      const index = this.selectedKeywords.indexOf(keyword)\n      if (index > -1) {\n        // 如果已选中，则取消选择\n        this.selectedKeywords.splice(index, 1)\n      } else {\n        // 如果未选中，检查是否超过最大数量\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(keyword)\n        } else {\n          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)\n        }\n      }\n    },\n\n    // 检查关键词是否已选中\n    isKeywordSelected(keyword) {\n      return this.selectedKeywords.includes(keyword)\n    },\n\n    // 切换分类选择状态\n    toggleCategorySelection(categoryName, categoryKeywords) {\n      // 检查该分类下的所有关键词是否都已选中\n      const allSelected = categoryKeywords.every(keyword => this.isKeywordSelected(keyword))\n\n      if (allSelected) {\n        // 如果都已选中，则取消选择该分类下的所有关键词\n        categoryKeywords.forEach(keyword => {\n          const index = this.selectedKeywords.indexOf(keyword)\n          if (index > -1) {\n            this.selectedKeywords.splice(index, 1)\n          }\n        })\n        this.$message.info(`已取消选择\"${categoryName}\"分类下的所有关键词`)\n      } else {\n        // 如果没有全部选中，则选择该分类下的所有关键词\n        categoryKeywords.forEach(keyword => {\n          if (!this.isKeywordSelected(keyword) && this.selectedKeywords.length < this.maxKeywords) {\n            this.selectedKeywords.push(keyword)\n          }\n        })\n\n        // 检查是否因为数量限制而无法全部选择\n        const notSelected = categoryKeywords.filter(keyword => !this.isKeywordSelected(keyword))\n        if (notSelected.length > 0) {\n          this.$message.warning(`由于数量限制，无法选择\"${categoryName}\"分类下的所有关键词`)\n        } else {\n          this.$message.success(`已选择\"${categoryName}\"分类下的所有关键词`)\n        }\n      }\n    },\n\n\n    // 前往下一步\n    goToNextStep() {\n      // 显示验证样式\n      this.showValidation = true\n\n      // 验证表单是否填写完整\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请填写具体需求')\n        return\n      }\n\n      if (this.selectedKeywords.length === 0) {\n        this.$message.warning('请至少选择一个关键词')\n        return\n      }\n\n      // 验证通过，隐藏验证样式并进入下一步\n      this.showValidation = false\n      if (this.currentStep < 2) {\n        this.currentStep++\n      }\n    },\n\n    // 返回上一步\n    goToPreviousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--\n      }\n    },\n\n    // 切换数据来源选择\n    toggleDataSource(source) {\n      const index = this.selectedDataSources.indexOf(source)\n      if (index > -1) {\n        this.selectedDataSources.splice(index, 1)\n      } else {\n        this.selectedDataSources.push(source)\n      }\n    },\n\n    // 显示新增数据源表单\n    showAddSourceForm() {\n      this.showAddSourceInput = true\n      this.newSourceUrl = ''\n    },\n\n    // 隐藏新增数据源表单\n    hideAddSourceForm() {\n      this.showAddSourceInput = false\n      this.newSourceUrl = ''\n    },\n\n    // 确认新增数据源\n    confirmAddSource() {\n      if (!this.newSourceUrl.trim()) {\n        this.$message.warning('请输入数据源网址')\n        return\n      }\n\n      // 简单的URL格式验证\n      const urlPattern = /^https?:\\/\\/.+/\n      if (!urlPattern.test(this.newSourceUrl.trim())) {\n        this.$message.warning('请输入有效的网址格式')\n        return\n      }\n\n      // 检查是否已存在相同的数据源\n      const trimmedUrl = this.newSourceUrl.trim()\n      if (this.customDataSources.includes(trimmedUrl)) {\n        this.$message.warning('该数据源已存在')\n        return\n      }\n\n      // 将新的数据源添加到自定义数据源列表中\n      this.customDataSources.push(trimmedUrl)\n      // 自动选中新添加的数据源\n      this.selectedDataSources.push(trimmedUrl)\n\n      this.$message.success('数据源添加成功')\n      // 清空输入框，但保持表单显示，允许继续添加\n      this.newSourceUrl = ''\n    },\n\n    // 删除自定义数据源\n    removeCustomSource(index) {\n      const sourceToRemove = this.customDataSources[index]\n      // 从自定义数据源列表中移除\n      this.customDataSources.splice(index, 1)\n      // 从已选择列表中移除\n      const selectedIndex = this.selectedDataSources.indexOf(sourceToRemove)\n      if (selectedIndex > -1) {\n        this.selectedDataSources.splice(selectedIndex, 1)\n      }\n      this.$message.success('数据源删除成功')\n    },\n\n    // 生成关联词\n    generateRelatedWords() {\n      // 检查是否填写了实体关键词和具体需求\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请先填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请先填写具体需求')\n        return\n      }\n\n      // 这里可以调用API生成关联词\n      this.$message.info('正在生成关联词...')\n\n      // 模拟生成关联词的过程\n      setTimeout(() => {\n        // 根据实体关键词生成相关的关联词\n        const generatedWords = [\n          '老板电器 售后服务',\n          '老板电器 三包义务',\n          '老板电器 客服态度',\n          '老板电器 质量',\n          '老板电器 燃气灶爆炸',\n          '老板电器 抽油烟机故障',\n          '老板电器 投诉处理',\n          '老板电器 对解',\n          '老板电器 投诉公示',\n          '老板电器 消费者不满',\n          '老板电器 不满',\n          '老板电器 投诉平台',\n          '老板电器 虚假宣传',\n          '老板电器 以次充好'\n        ]\n\n        // 保存所有生成的关键词\n        this.generatedKeywords = [...generatedWords]\n\n        // 默认选中前几个关键词（不超过最大数量）\n        this.selectedKeywords = []\n        generatedWords.forEach(word => {\n          if (this.selectedKeywords.length < this.maxKeywords) {\n            this.selectedKeywords.push(word)\n          }\n        })\n\n        this.$message.success('关联词生成成功')\n      }, 1000)\n    },\n\n    // 重新生成关联词\n    regenerateKeywords() {\n      // 直接调用生成关联词的方法\n      this.generateRelatedWords()\n    },\n\n    // 处理定时推送按钮点击\n    handleTimedPush() {\n      this.timedTaskDialogVisible = true\n    },\n\n    // 关闭定时任务弹窗\n    closeTimedTaskDialog() {\n      this.timedTaskDialogVisible = false\n    },\n\n    // 处理创建定时任务\n    handleCreateTimedTask() {\n      this.createTaskDialogVisible = true\n    },\n\n    // 处理添加定时任务按钮\n    handleAddTimedTask() {\n      this.createTaskDialogVisible = true\n    },\n\n    // 关闭创建任务弹窗\n    closeCreateTaskDialog() {\n      this.createTaskDialogVisible = false\n    },\n\n    // 保存并运行任务\n    saveAndRunTask() {\n      this.$message.success('任务已保存并开始运行')\n      this.createTaskDialogVisible = false\n      // TODO: 实现保存并运行逻辑\n    },\n\n    // 保存任务计划\n    saveTaskPlan() {\n      this.$message.success('任务计划已保存')\n      this.createTaskDialogVisible = false\n      // TODO: 实现保存计划逻辑\n    },\n\n    // 修改计划\n    modifyPlan() {\n      this.$message.info('修改计划功能开发中...')\n      // TODO: 实现修改计划逻辑\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n// 步骤指示器样式\n.steps-container {\n  background: white;\n  padding: 20px 24px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  .left-actions {\n    flex: 0 0 auto;\n\n    .timed-push-btn {\n      font-size: 14px;\n      padding: 8px 16px;\n      border-radius: 6px;\n      font-weight: 500;\n      transition: all 0.3s ease;\n\n      &:hover {\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    }\n  }\n\n  .steps-wrapper {\n    flex: 1;\n    display: flex;\n    justify-content: center;\n    gap: 60px;\n  }\n\n  .right-placeholder {\n    flex: 0 0 auto;\n    width: 88px; // 与左侧按钮宽度保持平衡\n  }\n\n  .step-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: #999;\n    font-size: 14px;\n\n    &.active {\n      color: #5470c6;\n      font-weight: 500;\n\n      .step-number {\n        background: #5470c6;\n        color: white;\n      }\n    }\n\n    .step-number {\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      background: #e8e8e8;\n      color: #999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      font-weight: 500;\n    }\n  }\n}\n\n.main-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 40px 24px;\n}\n\n// 分析来源区域\n.analysis-source {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 输入区域样式\n.input-section {\n  margin-bottom: 24px;\n\n  .input-label {\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n\n    .required {\n      color: #ff4d4f;\n      margin-left: 2px;\n    }\n\n    .keyword-count {\n      color: #999;\n      font-weight: normal;\n      margin-left: 8px;\n      font-size: 13px;\n\n      &.max-reached {\n        color: #ff4d4f;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .entity-input {\n    :deep(.el-input__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-input__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n\n  .requirement-textarea {\n    :deep(.el-textarea__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n      line-height: 1.6;\n      resize: vertical;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-textarea__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n}\n\n// 选择关联词区域\n.related-words-section {\n  .section-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 16px;\n\n    .header-left {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .section-label {\n        font-size: 14px;\n        color: #333;\n        font-weight: 500;\n      }\n\n      .word-count {\n        font-size: 14px;\n        color: #999;\n        font-weight: normal;\n        margin-left: 8px;\n        transition: color 0.3s ease;\n\n        &.max-reached {\n          color: #ff4d4f;\n          font-weight: 500;\n        }\n      }\n    }\n\n    .regenerate-btn {\n      font-size: 13px;\n      color: #5470c6;\n      padding: 4px 8px;\n      border-radius: 4px;\n      transition: all 0.3s ease;\n\n      &:hover {\n        background-color: #f0f7ff;\n        color: #4096ff;\n      }\n\n      i {\n        margin-right: 4px;\n        font-size: 12px;\n      }\n    }\n  }\n\n  .words-container {\n    text-align: center;\n\n    .generate-word-btn {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      padding: 8px 16px;\n      background: #f0f7ff;\n      color: #5470c6;\n      border: 1px dashed #5470c6;\n      border-radius: 4px;\n      font-size: 14px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n\n      &:hover {\n        background: #e6f4ff;\n        border-color: #4096ff;\n      }\n\n      i {\n        font-size: 12px;\n      }\n    }\n\n    .word-description {\n      font-size: 12px;\n      color: #999;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 关键词文本框包装器\n.keywords-textbox-wrapper {\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  padding: 16px;\n  background: #fff;\n  min-height: 120px;\n  transition: border-color 0.3s ease, box-shadow 0.3s ease;\n\n  &:hover {\n    border-color: #5470c6;\n  }\n\n  &:focus-within {\n    border-color: #5470c6;\n    box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n  }\n}\n\n// 生成的关键词显示区域\n.generated-keywords-display {\n  margin-bottom: 16px;\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n    margin-bottom: 16px;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .category-button {\n      min-width: 100px;\n      margin-right: 16px;\n      margin-bottom: 8px;\n      font-size: 13px;\n      border-radius: 16px;\n      transition: all 0.3s ease;\n\n      &:hover {\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n      }\n    }\n  }\n}\n\n\n\n// 关键词选择区域\n.keywords-selection-section {\n  .keywords-grid {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n  }\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n\n    .category-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      min-width: 80px;\n      padding-top: 6px;\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n\n        &.highlight {\n          background: #333;\n          color: white;\n          border-color: #333;\n          position: relative;\n          cursor: default;\n\n          &::after {\n            content: '';\n            position: absolute;\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 0;\n            height: 0;\n            border-left: 6px solid transparent;\n            border-right: 6px solid transparent;\n            border-top: 6px solid #333;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 第二步：数据概览样式\n.data-overview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n.data-source-section {\n  .source-option {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    padding: 16px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    .source-checkbox {\n      :deep(.el-checkbox__input) {\n        .el-checkbox__inner {\n          width: 18px;\n          height: 18px;\n          border-radius: 4px;\n          border: 2px solid #d9d9d9;\n\n          &::after {\n            width: 5px;\n            height: 9px;\n            left: 5px;\n            top: 1px;\n          }\n        }\n\n        &.is-checked .el-checkbox__inner {\n          background-color: #5470c6;\n          border-color: #5470c6;\n        }\n      }\n    }\n\n    .source-icon {\n      width: 40px;\n      height: 40px;\n      background: #f0f7ff;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      i {\n        font-size: 20px;\n        color: #5470c6;\n      }\n    }\n\n    .source-content {\n      flex: 1;\n\n      h3 {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n        margin: 0;\n        word-break: break-all;\n        line-height: 1.4;\n      }\n    }\n\n    .source-actions {\n      display: flex;\n      align-items: center;\n\n      .el-icon-delete {\n        font-size: 16px;\n        color: #999;\n        cursor: pointer;\n        transition: color 0.3s ease;\n\n        &:hover {\n          color: #ff4d4f;\n        }\n      }\n    }\n  }\n\n  .add-source-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    padding: 16px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    color: #999;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n\n    &:hover {\n      border-color: #5470c6;\n      color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    i {\n      font-size: 16px;\n    }\n  }\n}\n\n// 底部按钮区域\n.bottom-actions {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 16px;\n  padding-top: 24px;\n\n  .el-button {\n    padding: 12px 32px;\n    font-size: 16px;\n\n    &:disabled {\n      background-color: #f5f5f5;\n      border-color: #d9d9d9;\n      color: #bfbfbf;\n      cursor: not-allowed;\n    }\n  }\n}\n\n// 新增数据源表单样式\n.add-source-form {\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 8px;\n  padding: 20px;\n  margin-top: 16px;\n\n  .form-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n    padding-bottom: 12px;\n    border-bottom: 1px solid #f0f0f0;\n\n    h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .el-icon-close {\n      font-size: 18px;\n      color: #999;\n      cursor: pointer;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: #666;\n      }\n    }\n  }\n\n  .form-item {\n    .form-label {\n      display: block;\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      margin-bottom: 8px;\n\n      .required {\n        color: #ff4d4f;\n        margin-left: 2px;\n      }\n    }\n\n    .input-group {\n      display: flex;\n      gap: 12px;\n      align-items: flex-start;\n\n      .source-url-input {\n        flex: 1;\n\n        :deep(.el-input__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 12px 16px;\n          font-size: 14px;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n\n      .el-button {\n        padding: 12px 24px;\n        font-size: 14px;\n        border-radius: 6px;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .steps-container {\n    padding: 16px 12px;\n    flex-direction: column;\n    gap: 16px;\n\n    .left-actions {\n      align-self: flex-start;\n\n      .timed-push-btn {\n        font-size: 13px;\n        padding: 6px 12px;\n      }\n    }\n\n    .steps-wrapper {\n      gap: 30px;\n    }\n\n    .right-placeholder {\n      display: none;\n    }\n\n    .step-item {\n      font-size: 13px;\n    }\n  }\n\n  .main-content {\n    padding: 24px 16px;\n  }\n\n  .analysis-source {\n    padding: 24px 20px;\n  }\n\n  .document-content {\n    padding: 12px;\n    min-height: 100px;\n  }\n}\n\n// 定时任务抽屉样式\n.timed-task-drawer {\n  :deep(.el-drawer__header) {\n    padding: 20px 24px 16px;\n    border-bottom: 1px solid #f0f0f0;\n    margin-bottom: 0;\n  }\n\n  :deep(.el-drawer__body) {\n    padding: 0;\n  }\n}\n\n.drawer-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n\n  .drawer-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n  }\n\n  .add-task-btn {\n    font-size: 14px;\n    padding: 6px 12px;\n    border-radius: 4px;\n\n    .el-icon-plus {\n      margin-right: 4px;\n    }\n  }\n}\n\n.drawer-content {\n  padding: 24px;\n  min-height: 400px;\n}\n\n.empty-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400px;\n}\n\n.empty-content {\n  text-align: center;\n\n  .empty-icon {\n    margin-bottom: 24px;\n    display: flex;\n    justify-content: center;\n\n    svg {\n      opacity: 0.6;\n    }\n  }\n\n  .empty-text {\n    font-size: 16px;\n    color: #909399;\n    margin: 0 0 24px 0;\n    font-weight: 500;\n  }\n\n  .create-btn {\n    padding: 10px 24px;\n    font-size: 14px;\n    border-radius: 6px;\n    font-weight: 500;\n  }\n}\n\n// 创建任务弹窗样式\n.create-task-dialog {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n  }\n\n  :deep(.el-dialog__header) {\n    padding: 20px 24px 16px;\n    border-bottom: 1px solid #f0f0f0;\n  }\n\n  :deep(.el-dialog__body) {\n    padding: 24px;\n  }\n\n  :deep(.el-dialog__footer) {\n    padding: 16px 24px 24px;\n    border-top: 1px solid #f0f0f0;\n  }\n}\n\n.task-form {\n  .task-content {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    padding: 20px;\n    background: #f8f9fa;\n    border-radius: 8px;\n    margin-bottom: 24px;\n\n    .task-icon {\n      width: 48px;\n      height: 48px;\n      background: #5470c6;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      i {\n        font-size: 24px;\n        color: white;\n      }\n    }\n\n    .task-info {\n      flex: 1;\n\n      .task-name {\n        font-size: 16px;\n        font-weight: 600;\n        color: #333;\n        margin: 0 0 4px 0;\n      }\n\n      .task-description {\n        font-size: 14px;\n        color: #666;\n        margin: 0;\n        line-height: 1.4;\n      }\n    }\n  }\n\n  .execute-time-section {\n    .section-label {\n      font-size: 14px;\n      font-weight: 500;\n      color: #333;\n      margin-bottom: 12px;\n    }\n\n    .time-selector {\n      display: flex;\n      gap: 12px;\n      align-items: center;\n\n      .frequency-select {\n        width: 120px;\n      }\n\n      .time-picker {\n        width: 140px;\n      }\n    }\n  }\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n\n  .modify-btn {\n    color: #666;\n    border-color: #d9d9d9;\n\n    &:hover {\n      color: #5470c6;\n      border-color: #5470c6;\n    }\n  }\n\n  .run-btn {\n    background: #5470c6;\n    border-color: #5470c6;\n\n    &:hover {\n      background: #4096ff;\n      border-color: #4096ff;\n    }\n  }\n\n  .save-btn {\n    background: #52c41a;\n    border-color: #52c41a;\n\n    &:hover {\n      background: #73d13d;\n      border-color: #73d13d;\n    }\n  }\n}\n\n// 任务列表样式（待实现）\n// .task-list {\n//   // TODO: 任务列表样式\n// }\n</style>\n"]}]}