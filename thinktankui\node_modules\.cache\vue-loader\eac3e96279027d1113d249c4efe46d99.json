{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=style&index=0&id=040a21b8&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751529688975}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750933728705}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750933731152}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750933729640}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750933728031}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAumBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 步骤指示器 -->\n    <div class=\"steps-container\">\n      <!-- 左侧按钮区域 -->\n      <div class=\"left-actions\">\n        <el-button\n          class=\"timed-push-btn\"\n          type=\"primary\"\n          size=\"small\"\n          @click=\"handleTimedPush\"\n        >\n          定时推送\n        </el-button>\n      </div>\n\n      <!-- 步骤指示器 -->\n      <div class=\"steps-wrapper\">\n        <div class=\"step-item\" :class=\"{ active: currentStep === 1 }\">\n          <span class=\"step-number\">1</span>\n          <span class=\"step-text\">舆情分析来源</span>\n        </div>\n        <div class=\"step-item\" :class=\"{ active: currentStep === 2 }\">\n          <span class=\"step-number\">2</span>\n          <span class=\"step-text\">数据概览</span>\n        </div>\n      </div>\n\n      <!-- 右侧占位区域，保持布局平衡 -->\n      <div class=\"right-placeholder\"></div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 第一步：分析需求 -->\n      <div v-if=\"currentStep === 1\" class=\"analysis-source\">\n        <h2 class=\"section-title\">分析需求</h2>\n\n        <!-- 实体关键词区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            实体关键词\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"entityKeyword\"\n            placeholder=\"请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等\"\n            class=\"entity-input\"\n            :class=\"{ 'error': !entityKeyword.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 具体需求区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            具体需求\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"specificRequirement\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）\"\n            class=\"requirement-textarea\"\n            :class=\"{ 'error': !specificRequirement.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 选择关联词区域 -->\n        <div class=\"related-words-section\">\n          <div class=\"section-header\">\n            <div class=\"header-left\">\n              <span class=\"section-label\">选择关联词</span>\n              <span class=\"word-count\" :class=\"{ 'max-reached': selectedKeywords.length >= maxKeywords }\">\n                ({{ selectedKeywords.length }}/{{ maxKeywords }})\n              </span>\n            </div>\n            <el-button\n              v-if=\"generatedKeywords.length > 0\"\n              class=\"regenerate-btn\"\n              size=\"mini\"\n              type=\"text\"\n              @click=\"regenerateKeywords\"\n            >\n              <i class=\"el-icon-refresh\"></i>\n              重新生成\n            </el-button>\n          </div>\n\n          <div class=\"keywords-textbox-wrapper\">\n            <!-- 显示生成的关键词 -->\n            <div v-if=\"generatedKeywords.length > 0\" class=\"generated-keywords-display\">\n              <div v-for=\"(category, categoryName) in groupedKeywords\" :key=\"categoryName\" class=\"keyword-category\">\n                <el-button\n                  class=\"category-button\"\n                  size=\"small\"\n                  type=\"primary\"\n                  plain\n                  @click=\"toggleCategorySelection(categoryName, category)\"\n                >\n                  {{ categoryName }}\n                </el-button>\n                <div class=\"keyword-tags\">\n                  <el-tag\n                    v-for=\"(keyword, index) in category\"\n                    :key=\"index\"\n                    :class=\"['keyword-tag', { selected: isKeywordSelected(keyword) }]\"\n                    @click=\"toggleKeyword(keyword)\"\n                  >\n                    {{ keyword }}\n                  </el-tag>\n                </div>\n              </div>\n            </div>\n\n            <!-- 生成关联词按钮区域 -->\n            <div v-if=\"generatedKeywords.length === 0\" class=\"words-container\">\n              <div class=\"generate-word-btn\" @click=\"generateRelatedWords\">\n                <i class=\"el-icon-magic-stick\"></i>\n                <span>生成关联词</span>\n              </div>\n              <div class=\"word-description\">\n                根据你填写的需求和关键词生成关联词\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第二步：数据概览 -->\n      <div v-if=\"currentStep === 2\" class=\"data-overview\">\n        <h2 class=\"section-title\">选择数据来源</h2>\n\n        <!-- 数据来源选项 -->\n        <div class=\"data-source-section\">\n          <div class=\"source-option\" @click=\"toggleDataSource('online-search')\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"'online-search'\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-search\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>联网搜索</h3>\n            </div>\n          </div>\n\n          <!-- 自定义数据源列表 -->\n          <div v-for=\"(source, index) in customDataSources\" :key=\"index\" class=\"source-option\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"source\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-link\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>{{ source }}</h3>\n            </div>\n            <div class=\"source-actions\">\n              <i class=\"el-icon-delete\" @click=\"removeCustomSource(index)\"></i>\n            </div>\n          </div>\n\n          <!-- 新增数据源表单 -->\n          <div v-if=\"showAddSourceInput\" class=\"add-source-form\">\n            <div class=\"form-header\">\n              <h3>新增数据源</h3>\n              <i class=\"el-icon-close\" @click=\"hideAddSourceForm\"></i>\n            </div>\n            <div class=\"form-item\">\n              <label class=\"form-label\">\n                数据源网址\n                <span class=\"required\">*</span>\n              </label>\n              <div class=\"input-group\">\n                <el-input\n                  v-model=\"newSourceUrl\"\n                  placeholder=\"请输入网址，例如：https://www.example.com\"\n                  class=\"source-url-input\"\n                  @keyup.enter=\"confirmAddSource\"\n                />\n                <el-button type=\"primary\" @click=\"confirmAddSource\">确定</el-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- 新增来源按钮 -->\n          <div v-if=\"!showAddSourceInput\" class=\"add-source-btn\" @click=\"showAddSourceForm\">\n            <i class=\"el-icon-plus\"></i>\n            <span>新增来源</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 底部按钮区域 -->\n      <div class=\"bottom-actions\">\n        <el-button v-if=\"currentStep === 2\" @click=\"goToPreviousStep\" size=\"large\">上一步</el-button>\n        <el-button\n          v-if=\"currentStep === 1\"\n          @click=\"goToNextStep\"\n          type=\"primary\"\n          size=\"large\"\n          :disabled=\"!canGoToNextStep\"\n        >下一步</el-button>\n        <el-button v-if=\"currentStep === 2\" type=\"primary\" size=\"large\">开始分析</el-button>\n      </div>\n    </div>\n\n    <!-- 定时任务弹窗 -->\n    <el-dialog\n      title=\"定时任务\"\n      :visible.sync=\"timedTaskDialogVisible\"\n      width=\"600px\"\n      :before-close=\"closeTimedTaskDialog\"\n      class=\"timed-task-dialog\"\n    >\n      <!-- 弹窗头部右侧按钮 -->\n      <div slot=\"title\" class=\"dialog-header\">\n        <span class=\"dialog-title\">定时任务</span>\n        <el-button\n          type=\"primary\"\n          size=\"mini\"\n          icon=\"el-icon-plus\"\n          @click=\"handleAddTimedTask\"\n          class=\"add-task-btn\"\n        >\n          定时任务\n        </el-button>\n      </div>\n\n      <!-- 弹窗内容 -->\n      <div class=\"dialog-content\">\n        <!-- 空状态 -->\n        <div v-if=\"timedTaskList.length === 0\" class=\"empty-state\">\n          <div class=\"empty-content\">\n            <!-- 空状态图标 -->\n            <div class=\"empty-icon\">\n              <svg width=\"120\" height=\"120\" viewBox=\"0 0 120 120\" fill=\"none\">\n                <!-- 文件夹图标 -->\n                <path d=\"M20 30h25l5-10h50v70H20V30z\" fill=\"#f0f0f0\" stroke=\"#d0d0d0\" stroke-width=\"2\"/>\n                <path d=\"M25 35h70v50H25V35z\" fill=\"#fafafa\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <!-- 文档图标 -->\n                <rect x=\"35\" y=\"45\" width=\"30\" height=\"25\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\"/>\n                <rect x=\"70\" y=\"50\" width=\"20\" height=\"15\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\"/>\n                <!-- 装饰线条 -->\n                <line x1=\"40\" y1=\"52\" x2=\"60\" y2=\"52\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"40\" y1=\"57\" x2=\"55\" y2=\"57\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"40\" y1=\"62\" x2=\"58\" y2=\"62\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"75\" y1=\"55\" x2=\"85\" y2=\"55\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"75\" y1=\"60\" x2=\"82\" y2=\"60\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n              </svg>\n            </div>\n            <p class=\"empty-text\">暂无定时任务</p>\n            <el-button type=\"primary\" @click=\"handleCreateTimedTask\" class=\"create-btn\">\n              去创建\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 任务列表 -->\n        <div v-else class=\"task-list\">\n          <!-- TODO: 这里将来显示定时任务列表 -->\n        </div>\n      </div>\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      currentStep: 1, // 当前步骤\n      entityKeyword: '', // 实体关键词\n      specificRequirement: '', // 具体需求\n      selectedKeywords: [], // 已选择的关键词\n      generatedKeywords: [], // 生成的所有关键词\n      maxKeywords: 5, // 最大选择数量\n      selectedDataSources: ['online-search'], // 已选择的数据来源\n      customDataSources: [], // 自定义数据源列表\n      showAddSourceInput: false, // 显示新增数据源表单\n      newSourceUrl: '', // 新增数据源URL\n      showValidation: false, // 是否显示验证错误样式\n      timedTaskDialogVisible: false, // 定时任务弹窗显示状态\n      timedTaskList: [] // 定时任务列表\n    }\n  },\n  computed: {\n    // 检查是否可以进入下一步\n    canGoToNextStep() {\n      // 检查实体关键词是否填写\n      if (!this.entityKeyword.trim()) {\n        return false\n      }\n\n      // 检查具体需求是否填写\n      if (!this.specificRequirement.trim()) {\n        return false\n      }\n\n      // 检查是否至少选择了一个关键词\n      if (this.selectedKeywords.length === 0) {\n        return false\n      }\n\n      return true\n    },\n\n    // 将关键词按分类分组\n    groupedKeywords() {\n      if (this.generatedKeywords.length === 0) {\n        return {}\n      }\n\n      // 动态生成分类按钮\n      const categories = [\n        { name: '售后服务问题', keywords: [] },\n        { name: '产品质量问题', keywords: [] },\n        { name: '投诉处理结果', keywords: [] },\n        { name: '消费者不满', keywords: [] },\n        { name: '虚假宣传', keywords: [] }\n      ]\n\n      this.generatedKeywords.forEach(keyword => {\n        let assigned = false\n\n        categories.forEach(cat => {\n          if (cat.name === '售后服务问题' && (keyword.includes('售后') || keyword.includes('服务') || keyword.includes('客服'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '产品质量问题' && (keyword.includes('质量') || keyword.includes('爆炸') || keyword.includes('故障'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '投诉处理结果' && (keyword.includes('投诉') || keyword.includes('处理') || keyword.includes('对解'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '消费者不满' && (keyword.includes('不满') || keyword.includes('消费者'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '虚假宣传' && (keyword.includes('宣传') || keyword.includes('充好'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          }\n        })\n\n        if (!assigned) {\n          // 如果没有匹配的分类，添加到第一个有关键词的分类或创建新分类\n          if (categories[0].keywords.length === 0) {\n            categories[0].keywords.push(keyword)\n          } else {\n            categories.find(cat => cat.keywords.length > 0).keywords.push(keyword)\n          }\n        }\n      })\n\n      // 只返回有关键词的分类\n      const result = {}\n      categories.forEach(cat => {\n        if (cat.keywords.length > 0) {\n          result[cat.name] = cat.keywords\n        }\n      })\n\n      return result\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 切换关键词选择状态\n    toggleKeyword(keyword) {\n      const index = this.selectedKeywords.indexOf(keyword)\n      if (index > -1) {\n        // 如果已选中，则取消选择\n        this.selectedKeywords.splice(index, 1)\n      } else {\n        // 如果未选中，检查是否超过最大数量\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(keyword)\n        } else {\n          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)\n        }\n      }\n    },\n\n    // 检查关键词是否已选中\n    isKeywordSelected(keyword) {\n      return this.selectedKeywords.includes(keyword)\n    },\n\n    // 切换分类选择状态\n    toggleCategorySelection(categoryName, categoryKeywords) {\n      // 检查该分类下的所有关键词是否都已选中\n      const allSelected = categoryKeywords.every(keyword => this.isKeywordSelected(keyword))\n\n      if (allSelected) {\n        // 如果都已选中，则取消选择该分类下的所有关键词\n        categoryKeywords.forEach(keyword => {\n          const index = this.selectedKeywords.indexOf(keyword)\n          if (index > -1) {\n            this.selectedKeywords.splice(index, 1)\n          }\n        })\n        this.$message.info(`已取消选择\"${categoryName}\"分类下的所有关键词`)\n      } else {\n        // 如果没有全部选中，则选择该分类下的所有关键词\n        categoryKeywords.forEach(keyword => {\n          if (!this.isKeywordSelected(keyword) && this.selectedKeywords.length < this.maxKeywords) {\n            this.selectedKeywords.push(keyword)\n          }\n        })\n\n        // 检查是否因为数量限制而无法全部选择\n        const notSelected = categoryKeywords.filter(keyword => !this.isKeywordSelected(keyword))\n        if (notSelected.length > 0) {\n          this.$message.warning(`由于数量限制，无法选择\"${categoryName}\"分类下的所有关键词`)\n        } else {\n          this.$message.success(`已选择\"${categoryName}\"分类下的所有关键词`)\n        }\n      }\n    },\n\n\n    // 前往下一步\n    goToNextStep() {\n      // 显示验证样式\n      this.showValidation = true\n\n      // 验证表单是否填写完整\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请填写具体需求')\n        return\n      }\n\n      if (this.selectedKeywords.length === 0) {\n        this.$message.warning('请至少选择一个关键词')\n        return\n      }\n\n      // 验证通过，隐藏验证样式并进入下一步\n      this.showValidation = false\n      if (this.currentStep < 2) {\n        this.currentStep++\n      }\n    },\n\n    // 返回上一步\n    goToPreviousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--\n      }\n    },\n\n    // 切换数据来源选择\n    toggleDataSource(source) {\n      const index = this.selectedDataSources.indexOf(source)\n      if (index > -1) {\n        this.selectedDataSources.splice(index, 1)\n      } else {\n        this.selectedDataSources.push(source)\n      }\n    },\n\n    // 显示新增数据源表单\n    showAddSourceForm() {\n      this.showAddSourceInput = true\n      this.newSourceUrl = ''\n    },\n\n    // 隐藏新增数据源表单\n    hideAddSourceForm() {\n      this.showAddSourceInput = false\n      this.newSourceUrl = ''\n    },\n\n    // 确认新增数据源\n    confirmAddSource() {\n      if (!this.newSourceUrl.trim()) {\n        this.$message.warning('请输入数据源网址')\n        return\n      }\n\n      // 简单的URL格式验证\n      const urlPattern = /^https?:\\/\\/.+/\n      if (!urlPattern.test(this.newSourceUrl.trim())) {\n        this.$message.warning('请输入有效的网址格式')\n        return\n      }\n\n      // 检查是否已存在相同的数据源\n      const trimmedUrl = this.newSourceUrl.trim()\n      if (this.customDataSources.includes(trimmedUrl)) {\n        this.$message.warning('该数据源已存在')\n        return\n      }\n\n      // 将新的数据源添加到自定义数据源列表中\n      this.customDataSources.push(trimmedUrl)\n      // 自动选中新添加的数据源\n      this.selectedDataSources.push(trimmedUrl)\n\n      this.$message.success('数据源添加成功')\n      // 清空输入框，但保持表单显示，允许继续添加\n      this.newSourceUrl = ''\n    },\n\n    // 删除自定义数据源\n    removeCustomSource(index) {\n      const sourceToRemove = this.customDataSources[index]\n      // 从自定义数据源列表中移除\n      this.customDataSources.splice(index, 1)\n      // 从已选择列表中移除\n      const selectedIndex = this.selectedDataSources.indexOf(sourceToRemove)\n      if (selectedIndex > -1) {\n        this.selectedDataSources.splice(selectedIndex, 1)\n      }\n      this.$message.success('数据源删除成功')\n    },\n\n    // 生成关联词\n    generateRelatedWords() {\n      // 检查是否填写了实体关键词和具体需求\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请先填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请先填写具体需求')\n        return\n      }\n\n      // 这里可以调用API生成关联词\n      this.$message.info('正在生成关联词...')\n\n      // 模拟生成关联词的过程\n      setTimeout(() => {\n        // 根据实体关键词生成相关的关联词\n        const generatedWords = [\n          '老板电器 售后服务',\n          '老板电器 三包义务',\n          '老板电器 客服态度',\n          '老板电器 质量',\n          '老板电器 燃气灶爆炸',\n          '老板电器 抽油烟机故障',\n          '老板电器 投诉处理',\n          '老板电器 对解',\n          '老板电器 投诉公示',\n          '老板电器 消费者不满',\n          '老板电器 不满',\n          '老板电器 投诉平台',\n          '老板电器 虚假宣传',\n          '老板电器 以次充好'\n        ]\n\n        // 保存所有生成的关键词\n        this.generatedKeywords = [...generatedWords]\n\n        // 默认选中前几个关键词（不超过最大数量）\n        this.selectedKeywords = []\n        generatedWords.forEach(word => {\n          if (this.selectedKeywords.length < this.maxKeywords) {\n            this.selectedKeywords.push(word)\n          }\n        })\n\n        this.$message.success('关联词生成成功')\n      }, 1000)\n    },\n\n    // 重新生成关联词\n    regenerateKeywords() {\n      // 直接调用生成关联词的方法\n      this.generateRelatedWords()\n    },\n\n    // 处理定时推送按钮点击\n    handleTimedPush() {\n      this.timedTaskDialogVisible = true\n    },\n\n    // 关闭定时任务弹窗\n    closeTimedTaskDialog() {\n      this.timedTaskDialogVisible = false\n    },\n\n    // 处理创建定时任务\n    handleCreateTimedTask() {\n      this.$message.info('创建定时任务功能开发中...')\n      // TODO: 实现创建定时任务功能\n    },\n\n    // 处理添加定时任务按钮\n    handleAddTimedTask() {\n      this.$message.info('添加定时任务功能开发中...')\n      // TODO: 实现添加定时任务功能\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n// 步骤指示器样式\n.steps-container {\n  background: white;\n  padding: 20px 24px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  .left-actions {\n    flex: 0 0 auto;\n\n    .timed-push-btn {\n      font-size: 14px;\n      padding: 8px 16px;\n      border-radius: 6px;\n      font-weight: 500;\n      transition: all 0.3s ease;\n\n      &:hover {\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    }\n  }\n\n  .steps-wrapper {\n    flex: 1;\n    display: flex;\n    justify-content: center;\n    gap: 60px;\n  }\n\n  .right-placeholder {\n    flex: 0 0 auto;\n    width: 88px; // 与左侧按钮宽度保持平衡\n  }\n\n  .step-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: #999;\n    font-size: 14px;\n\n    &.active {\n      color: #5470c6;\n      font-weight: 500;\n\n      .step-number {\n        background: #5470c6;\n        color: white;\n      }\n    }\n\n    .step-number {\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      background: #e8e8e8;\n      color: #999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      font-weight: 500;\n    }\n  }\n}\n\n.main-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 40px 24px;\n}\n\n// 分析来源区域\n.analysis-source {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 输入区域样式\n.input-section {\n  margin-bottom: 24px;\n\n  .input-label {\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n\n    .required {\n      color: #ff4d4f;\n      margin-left: 2px;\n    }\n\n    .keyword-count {\n      color: #999;\n      font-weight: normal;\n      margin-left: 8px;\n      font-size: 13px;\n\n      &.max-reached {\n        color: #ff4d4f;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .entity-input {\n    :deep(.el-input__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-input__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n\n  .requirement-textarea {\n    :deep(.el-textarea__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n      line-height: 1.6;\n      resize: vertical;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-textarea__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n}\n\n// 选择关联词区域\n.related-words-section {\n  .section-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 16px;\n\n    .header-left {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .section-label {\n        font-size: 14px;\n        color: #333;\n        font-weight: 500;\n      }\n\n      .word-count {\n        font-size: 14px;\n        color: #999;\n        font-weight: normal;\n        margin-left: 8px;\n        transition: color 0.3s ease;\n\n        &.max-reached {\n          color: #ff4d4f;\n          font-weight: 500;\n        }\n      }\n    }\n\n    .regenerate-btn {\n      font-size: 13px;\n      color: #5470c6;\n      padding: 4px 8px;\n      border-radius: 4px;\n      transition: all 0.3s ease;\n\n      &:hover {\n        background-color: #f0f7ff;\n        color: #4096ff;\n      }\n\n      i {\n        margin-right: 4px;\n        font-size: 12px;\n      }\n    }\n  }\n\n  .words-container {\n    text-align: center;\n\n    .generate-word-btn {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      padding: 8px 16px;\n      background: #f0f7ff;\n      color: #5470c6;\n      border: 1px dashed #5470c6;\n      border-radius: 4px;\n      font-size: 14px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n\n      &:hover {\n        background: #e6f4ff;\n        border-color: #4096ff;\n      }\n\n      i {\n        font-size: 12px;\n      }\n    }\n\n    .word-description {\n      font-size: 12px;\n      color: #999;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 关键词文本框包装器\n.keywords-textbox-wrapper {\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  padding: 16px;\n  background: #fff;\n  min-height: 120px;\n  transition: border-color 0.3s ease, box-shadow 0.3s ease;\n\n  &:hover {\n    border-color: #5470c6;\n  }\n\n  &:focus-within {\n    border-color: #5470c6;\n    box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n  }\n}\n\n// 生成的关键词显示区域\n.generated-keywords-display {\n  margin-bottom: 16px;\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n    margin-bottom: 16px;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .category-button {\n      min-width: 100px;\n      margin-right: 16px;\n      margin-bottom: 8px;\n      font-size: 13px;\n      border-radius: 16px;\n      transition: all 0.3s ease;\n\n      &:hover {\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n      }\n    }\n  }\n}\n\n\n\n// 关键词选择区域\n.keywords-selection-section {\n  .keywords-grid {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n  }\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n\n    .category-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      min-width: 80px;\n      padding-top: 6px;\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n\n        &.highlight {\n          background: #333;\n          color: white;\n          border-color: #333;\n          position: relative;\n          cursor: default;\n\n          &::after {\n            content: '';\n            position: absolute;\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 0;\n            height: 0;\n            border-left: 6px solid transparent;\n            border-right: 6px solid transparent;\n            border-top: 6px solid #333;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 第二步：数据概览样式\n.data-overview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n.data-source-section {\n  .source-option {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    padding: 16px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    .source-checkbox {\n      :deep(.el-checkbox__input) {\n        .el-checkbox__inner {\n          width: 18px;\n          height: 18px;\n          border-radius: 4px;\n          border: 2px solid #d9d9d9;\n\n          &::after {\n            width: 5px;\n            height: 9px;\n            left: 5px;\n            top: 1px;\n          }\n        }\n\n        &.is-checked .el-checkbox__inner {\n          background-color: #5470c6;\n          border-color: #5470c6;\n        }\n      }\n    }\n\n    .source-icon {\n      width: 40px;\n      height: 40px;\n      background: #f0f7ff;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      i {\n        font-size: 20px;\n        color: #5470c6;\n      }\n    }\n\n    .source-content {\n      flex: 1;\n\n      h3 {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n        margin: 0;\n        word-break: break-all;\n        line-height: 1.4;\n      }\n    }\n\n    .source-actions {\n      display: flex;\n      align-items: center;\n\n      .el-icon-delete {\n        font-size: 16px;\n        color: #999;\n        cursor: pointer;\n        transition: color 0.3s ease;\n\n        &:hover {\n          color: #ff4d4f;\n        }\n      }\n    }\n  }\n\n  .add-source-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    padding: 16px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    color: #999;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n\n    &:hover {\n      border-color: #5470c6;\n      color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    i {\n      font-size: 16px;\n    }\n  }\n}\n\n// 底部按钮区域\n.bottom-actions {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 16px;\n  padding-top: 24px;\n\n  .el-button {\n    padding: 12px 32px;\n    font-size: 16px;\n\n    &:disabled {\n      background-color: #f5f5f5;\n      border-color: #d9d9d9;\n      color: #bfbfbf;\n      cursor: not-allowed;\n    }\n  }\n}\n\n// 新增数据源表单样式\n.add-source-form {\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 8px;\n  padding: 20px;\n  margin-top: 16px;\n\n  .form-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n    padding-bottom: 12px;\n    border-bottom: 1px solid #f0f0f0;\n\n    h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .el-icon-close {\n      font-size: 18px;\n      color: #999;\n      cursor: pointer;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: #666;\n      }\n    }\n  }\n\n  .form-item {\n    .form-label {\n      display: block;\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      margin-bottom: 8px;\n\n      .required {\n        color: #ff4d4f;\n        margin-left: 2px;\n      }\n    }\n\n    .input-group {\n      display: flex;\n      gap: 12px;\n      align-items: flex-start;\n\n      .source-url-input {\n        flex: 1;\n\n        :deep(.el-input__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 12px 16px;\n          font-size: 14px;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n\n      .el-button {\n        padding: 12px 24px;\n        font-size: 14px;\n        border-radius: 6px;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .steps-container {\n    padding: 16px 12px;\n    flex-direction: column;\n    gap: 16px;\n\n    .left-actions {\n      align-self: flex-start;\n\n      .timed-push-btn {\n        font-size: 13px;\n        padding: 6px 12px;\n      }\n    }\n\n    .steps-wrapper {\n      gap: 30px;\n    }\n\n    .right-placeholder {\n      display: none;\n    }\n\n    .step-item {\n      font-size: 13px;\n    }\n  }\n\n  .main-content {\n    padding: 24px 16px;\n  }\n\n  .analysis-source {\n    padding: 24px 20px;\n  }\n\n  .document-content {\n    padding: 12px;\n    min-height: 100px;\n  }\n}\n\n// 定时任务弹窗样式\n.timed-task-dialog {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n\n    .el-dialog__header {\n      padding: 20px 24px 16px;\n      border-bottom: 1px solid #f0f0f0;\n    }\n\n    .el-dialog__body {\n      padding: 0;\n    }\n  }\n}\n\n.dialog-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n\n  .dialog-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n  }\n\n  .add-task-btn {\n    font-size: 14px;\n    padding: 6px 12px;\n    border-radius: 4px;\n\n    .el-icon-plus {\n      margin-right: 4px;\n    }\n  }\n}\n\n.dialog-content {\n  padding: 24px;\n  min-height: 400px;\n}\n\n.empty-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400px;\n}\n\n.empty-content {\n  text-align: center;\n\n  .empty-icon {\n    margin-bottom: 24px;\n    display: flex;\n    justify-content: center;\n\n    svg {\n      opacity: 0.6;\n    }\n  }\n\n  .empty-text {\n    font-size: 16px;\n    color: #909399;\n    margin: 0 0 24px 0;\n    font-weight: 500;\n  }\n\n  .create-btn {\n    padding: 10px 24px;\n    font-size: 14px;\n    border-radius: 6px;\n    font-weight: 500;\n  }\n}\n\n// 任务列表样式（待实现）\n// .task-list {\n//   // TODO: 任务列表样式\n// }\n</style>\n"]}]}