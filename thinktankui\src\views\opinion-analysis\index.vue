<template>
  <div class="opinion-analysis">
    <!-- 步骤指示器 -->
    <div class="steps-container">
      <!-- 左侧占位区域，保持布局平衡 -->
      <div class="left-placeholder"></div>

      <!-- 步骤指示器 -->
      <div class="steps-wrapper">
        <div class="step-item" :class="{ active: currentStep === 1 }">
          <span class="step-number">1</span>
          <span class="step-text">舆情分析来源</span>
        </div>
        <div class="step-item" :class="{ active: currentStep === 2 }">
          <span class="step-number">2</span>
          <span class="step-text">数据概览</span>
        </div>
      </div>

      <!-- 右侧按钮区域 -->
      <div class="right-actions">
        <el-button
          class="timed-push-btn"
          type="primary"
          size="small"
          @click="handleTimedPush"
        >
          定时推送
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 第一步：分析需求 -->
      <div v-if="currentStep === 1" class="analysis-source">
        <h2 class="section-title">分析需求</h2>

        <!-- 实体关键词区域 -->
        <div class="input-section">
          <div class="input-label">
            实体关键词
            <span class="required">*</span>
          </div>
          <el-input
            v-model="entityKeyword"
            placeholder="请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等"
            class="entity-input"
            :class="{ 'error': !entityKeyword.trim() && showValidation }"
          />
        </div>

        <!-- 具体需求区域 -->
        <div class="input-section">
          <div class="input-label">
            具体需求
            <span class="required">*</span>
          </div>
          <el-input
            v-model="specificRequirement"
            type="textarea"
            :rows="4"
            placeholder="请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）"
            class="requirement-textarea"
            :class="{ 'error': !specificRequirement.trim() && showValidation }"
          />
        </div>

        <!-- 选择关联词区域 -->
        <div class="related-words-section">
          <div class="section-header">
            <div class="header-left">
              <span class="section-label">选择关联词</span>
              <span class="word-count" :class="{ 'max-reached': selectedKeywords.length >= maxKeywords }">
                ({{ selectedKeywords.length }}/{{ maxKeywords }})
              </span>
            </div>
            <el-button
              v-if="generatedKeywords.length > 0"
              class="regenerate-btn"
              size="mini"
              type="text"
              @click="regenerateKeywords"
            >
              <i class="el-icon-refresh"></i>
              重新生成
            </el-button>
          </div>

          <div class="keywords-textbox-wrapper">
            <!-- 显示生成的关键词 -->
            <div v-if="generatedKeywords.length > 0" class="generated-keywords-display">
              <div v-for="(category, categoryName) in groupedKeywords" :key="categoryName" class="keyword-category">
                <el-button
                  class="category-button"
                  size="small"
                  type="primary"
                  plain
                  @click="toggleCategorySelection(categoryName, category)"
                >
                  {{ categoryName }}
                </el-button>
                <div class="keyword-tags">
                  <el-tag
                    v-for="(keyword, index) in category"
                    :key="index"
                    :class="['keyword-tag', { selected: isKeywordSelected(keyword) }]"
                    @click="toggleKeyword(keyword)"
                  >
                    {{ keyword }}
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 生成关联词按钮区域 -->
            <div v-if="generatedKeywords.length === 0" class="words-container">
              <div class="generate-word-btn" @click="generateRelatedWords">
                <i class="el-icon-magic-stick"></i>
                <span>生成关联词</span>
              </div>
              <div class="word-description">
                根据你填写的需求和关键词生成关联词
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二步：数据概览 -->
      <div v-if="currentStep === 2" class="data-overview">
        <h2 class="section-title">选择数据来源</h2>

        <!-- 数据来源选项 -->
        <div class="data-source-section">
          <div class="source-option" @click="toggleDataSource('online-search')">
            <el-checkbox
              v-model="selectedDataSources"
              :value="'online-search'"
              class="source-checkbox"
            ></el-checkbox>
            <div class="source-icon">
              <i class="el-icon-search"></i>
            </div>
            <div class="source-content">
              <h3>联网搜索</h3>
            </div>
          </div>

          <!-- 自定义数据源列表 -->
          <div v-for="(source, index) in customDataSources" :key="index" class="source-option">
            <el-checkbox
              v-model="selectedDataSources"
              :value="source"
              class="source-checkbox"
            ></el-checkbox>
            <div class="source-icon">
              <i class="el-icon-link"></i>
            </div>
            <div class="source-content">
              <h3>{{ source }}</h3>
            </div>
            <div class="source-actions">
              <i class="el-icon-delete" @click="removeCustomSource(index)"></i>
            </div>
          </div>

          <!-- 新增数据源表单 -->
          <div v-if="showAddSourceInput" class="add-source-form">
            <div class="form-header">
              <h3>新增数据源</h3>
              <i class="el-icon-close" @click="hideAddSourceForm"></i>
            </div>
            <div class="form-item">
              <label class="form-label">
                数据源网址
                <span class="required">*</span>
              </label>
              <div class="input-group">
                <el-input
                  v-model="newSourceUrl"
                  placeholder="请输入网址，例如：https://www.example.com"
                  class="source-url-input"
                  @keyup.enter="confirmAddSource"
                />
                <el-button type="primary" @click="confirmAddSource">确定</el-button>
              </div>
            </div>
          </div>

          <!-- 新增来源按钮 -->
          <div v-if="!showAddSourceInput" class="add-source-btn" @click="showAddSourceForm">
            <i class="el-icon-plus"></i>
            <span>新增来源</span>
          </div>
        </div>
      </div>

      <!-- 底部按钮区域 -->
      <div class="bottom-actions">
        <el-button v-if="currentStep === 2" @click="goToPreviousStep" size="large">上一步</el-button>
        <el-button
          v-if="currentStep === 1"
          @click="goToNextStep"
          type="primary"
          size="large"
          :disabled="!canGoToNextStep"
        >下一步</el-button>
        <el-button v-if="currentStep === 2" type="primary" size="large">开始分析</el-button>
      </div>
    </div>

    <!-- 定时任务抽屉 -->
    <el-drawer
      title="定时任务"
      :visible.sync="timedTaskDialogVisible"
      direction="rtl"
      size="600px"
      :before-close="closeTimedTaskDialog"
      custom-class="timed-task-drawer"
    >
      <!-- 抽屉头部右侧按钮 -->
      <div slot="title" class="drawer-header">
        <span class="drawer-title">定时任务</span>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="handleAddTimedTask"
          class="add-task-btn"
        >
          定时任务
        </el-button>
      </div>

      <!-- 抽屉内容 -->
      <div class="drawer-content">
        <!-- 空状态 -->
        <div v-if="timedTaskList.length === 0" class="empty-state">
          <div class="empty-content">
            <!-- 空状态图标 -->
            <div class="empty-icon">
              <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
                <!-- 文件夹图标 -->
                <path d="M20 30h25l5-10h50v70H20V30z" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="2"/>
                <path d="M25 35h70v50H25V35z" fill="#fafafa" stroke="#e0e0e0" stroke-width="1"/>
                <!-- 文档图标 -->
                <rect x="35" y="45" width="30" height="25" fill="#ffffff" stroke="#d0d0d0" stroke-width="1" rx="2"/>
                <rect x="70" y="50" width="20" height="15" fill="#ffffff" stroke="#d0d0d0" stroke-width="1" rx="2"/>
                <!-- 装饰线条 -->
                <line x1="40" y1="52" x2="60" y2="52" stroke="#e0e0e0" stroke-width="1"/>
                <line x1="40" y1="57" x2="55" y2="57" stroke="#e0e0e0" stroke-width="1"/>
                <line x1="40" y1="62" x2="58" y2="62" stroke="#e0e0e0" stroke-width="1"/>
                <line x1="75" y1="55" x2="85" y2="55" stroke="#e0e0e0" stroke-width="1"/>
                <line x1="75" y1="60" x2="82" y2="60" stroke="#e0e0e0" stroke-width="1"/>
              </svg>
            </div>
            <p class="empty-text">暂无定时任务</p>
            <el-button type="primary" @click="handleCreateTimedTask" class="create-btn">
              去创建
            </el-button>
          </div>
        </div>

        <!-- 任务列表 -->
        <div v-else class="task-list">
          <!-- TODO: 这里将来显示定时任务列表 -->
        </div>
      </div>

      <!-- 创建任务弹窗 -->
      <el-dialog
        title="定时任务"
        :visible.sync="createTaskDialogVisible"
        width="500px"
        :before-close="closeCreateTaskDialog"
        :append-to-body="false"
        class="create-task-dialog"
      >
        <div class="task-form">
          <!-- 任务内容 -->
          <div class="task-content">
            <div class="task-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="task-info">
              <h3 class="task-name">{{ taskForm.name }}</h3>
              <p class="task-description">基于当前分析需求自动生成AI新闻总结</p>
            </div>
          </div>

          <!-- 执行时间 -->
          <div class="execute-time-section">
            <div class="section-label">执行时间</div>
            <div class="time-selector">
              <el-select v-model="taskForm.frequency" placeholder="选择频率" class="frequency-select">
                <el-option label="每天" value="daily"></el-option>
                <el-option label="每周" value="weekly"></el-option>
                <el-option label="每月" value="monthly"></el-option>
              </el-select>
              <el-time-picker
                v-model="taskForm.executeTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="选择时间"
                class="time-picker"
              >
              </el-time-picker>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div slot="footer" class="dialog-footer">
          <el-button @click="modifyPlan" class="modify-btn">修改计划</el-button>
          <el-button type="primary" @click="saveAndRunTask" class="run-btn">保存并运行</el-button>
          <el-button type="success" @click="saveTaskPlan" class="save-btn">保存计划</el-button>
        </div>
      </el-dialog>
    </el-drawer>

  </div>
</template>

<script>
export default {
  name: 'OpinionAnalysis',
  data() {
    return {
      currentStep: 1, // 当前步骤
      entityKeyword: '', // 实体关键词
      specificRequirement: '', // 具体需求
      selectedKeywords: [], // 已选择的关键词
      generatedKeywords: [], // 生成的所有关键词
      maxKeywords: 5, // 最大选择数量
      selectedDataSources: ['online-search'], // 已选择的数据来源
      customDataSources: [], // 自定义数据源列表
      showAddSourceInput: false, // 显示新增数据源表单
      newSourceUrl: '', // 新增数据源URL
      showValidation: false, // 是否显示验证错误样式
      timedTaskDialogVisible: false, // 定时任务抽屉显示状态
      timedTaskList: [], // 定时任务列表
      createTaskDialogVisible: false, // 创建任务弹窗显示状态
      taskForm: {
        name: '总结AI新闻',
        executeTime: '16:00',
        frequency: 'daily'
      }
    }
  },
  computed: {
    // 检查是否可以进入下一步
    canGoToNextStep() {
      // 检查实体关键词是否填写
      if (!this.entityKeyword.trim()) {
        return false
      }

      // 检查具体需求是否填写
      if (!this.specificRequirement.trim()) {
        return false
      }

      // 检查是否至少选择了一个关键词
      if (this.selectedKeywords.length === 0) {
        return false
      }

      return true
    },

    // 将关键词按分类分组
    groupedKeywords() {
      if (this.generatedKeywords.length === 0) {
        return {}
      }

      // 动态生成分类按钮
      const categories = [
        { name: '售后服务问题', keywords: [] },
        { name: '产品质量问题', keywords: [] },
        { name: '投诉处理结果', keywords: [] },
        { name: '消费者不满', keywords: [] },
        { name: '虚假宣传', keywords: [] }
      ]

      this.generatedKeywords.forEach(keyword => {
        let assigned = false

        categories.forEach(cat => {
          if (cat.name === '售后服务问题' && (keyword.includes('售后') || keyword.includes('服务') || keyword.includes('客服'))) {
            cat.keywords.push(keyword)
            assigned = true
          } else if (cat.name === '产品质量问题' && (keyword.includes('质量') || keyword.includes('爆炸') || keyword.includes('故障'))) {
            cat.keywords.push(keyword)
            assigned = true
          } else if (cat.name === '投诉处理结果' && (keyword.includes('投诉') || keyword.includes('处理') || keyword.includes('对解'))) {
            cat.keywords.push(keyword)
            assigned = true
          } else if (cat.name === '消费者不满' && (keyword.includes('不满') || keyword.includes('消费者'))) {
            cat.keywords.push(keyword)
            assigned = true
          } else if (cat.name === '虚假宣传' && (keyword.includes('宣传') || keyword.includes('充好'))) {
            cat.keywords.push(keyword)
            assigned = true
          }
        })

        if (!assigned) {
          // 如果没有匹配的分类，添加到第一个有关键词的分类或创建新分类
          if (categories[0].keywords.length === 0) {
            categories[0].keywords.push(keyword)
          } else {
            categories.find(cat => cat.keywords.length > 0).keywords.push(keyword)
          }
        }
      })

      // 只返回有关键词的分类
      const result = {}
      categories.forEach(cat => {
        if (cat.keywords.length > 0) {
          result[cat.name] = cat.keywords
        }
      })

      return result
    }
  },
  mounted() {
    // 页面初始化逻辑
    console.log('舆情分析页面已加载')
  },
  methods: {
    // 切换关键词选择状态
    toggleKeyword(keyword) {
      const index = this.selectedKeywords.indexOf(keyword)
      if (index > -1) {
        // 如果已选中，则取消选择
        this.selectedKeywords.splice(index, 1)
      } else {
        // 如果未选中，检查是否超过最大数量
        if (this.selectedKeywords.length < this.maxKeywords) {
          this.selectedKeywords.push(keyword)
        } else {
          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)
        }
      }
    },

    // 检查关键词是否已选中
    isKeywordSelected(keyword) {
      return this.selectedKeywords.includes(keyword)
    },

    // 切换分类选择状态
    toggleCategorySelection(categoryName, categoryKeywords) {
      // 检查该分类下的所有关键词是否都已选中
      const allSelected = categoryKeywords.every(keyword => this.isKeywordSelected(keyword))

      if (allSelected) {
        // 如果都已选中，则取消选择该分类下的所有关键词
        categoryKeywords.forEach(keyword => {
          const index = this.selectedKeywords.indexOf(keyword)
          if (index > -1) {
            this.selectedKeywords.splice(index, 1)
          }
        })
        this.$message.info(`已取消选择"${categoryName}"分类下的所有关键词`)
      } else {
        // 如果没有全部选中，则选择该分类下的所有关键词
        categoryKeywords.forEach(keyword => {
          if (!this.isKeywordSelected(keyword) && this.selectedKeywords.length < this.maxKeywords) {
            this.selectedKeywords.push(keyword)
          }
        })

        // 检查是否因为数量限制而无法全部选择
        const notSelected = categoryKeywords.filter(keyword => !this.isKeywordSelected(keyword))
        if (notSelected.length > 0) {
          this.$message.warning(`由于数量限制，无法选择"${categoryName}"分类下的所有关键词`)
        } else {
          this.$message.success(`已选择"${categoryName}"分类下的所有关键词`)
        }
      }
    },


    // 前往下一步
    goToNextStep() {
      // 显示验证样式
      this.showValidation = true

      // 验证表单是否填写完整
      if (!this.entityKeyword.trim()) {
        this.$message.warning('请填写实体关键词')
        return
      }

      if (!this.specificRequirement.trim()) {
        this.$message.warning('请填写具体需求')
        return
      }

      if (this.selectedKeywords.length === 0) {
        this.$message.warning('请至少选择一个关键词')
        return
      }

      // 验证通过，隐藏验证样式并进入下一步
      this.showValidation = false
      if (this.currentStep < 2) {
        this.currentStep++
      }
    },

    // 返回上一步
    goToPreviousStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },

    // 切换数据来源选择
    toggleDataSource(source) {
      const index = this.selectedDataSources.indexOf(source)
      if (index > -1) {
        this.selectedDataSources.splice(index, 1)
      } else {
        this.selectedDataSources.push(source)
      }
    },

    // 显示新增数据源表单
    showAddSourceForm() {
      this.showAddSourceInput = true
      this.newSourceUrl = ''
    },

    // 隐藏新增数据源表单
    hideAddSourceForm() {
      this.showAddSourceInput = false
      this.newSourceUrl = ''
    },

    // 确认新增数据源
    confirmAddSource() {
      if (!this.newSourceUrl.trim()) {
        this.$message.warning('请输入数据源网址')
        return
      }

      // 简单的URL格式验证
      const urlPattern = /^https?:\/\/.+/
      if (!urlPattern.test(this.newSourceUrl.trim())) {
        this.$message.warning('请输入有效的网址格式')
        return
      }

      // 检查是否已存在相同的数据源
      const trimmedUrl = this.newSourceUrl.trim()
      if (this.customDataSources.includes(trimmedUrl)) {
        this.$message.warning('该数据源已存在')
        return
      }

      // 将新的数据源添加到自定义数据源列表中
      this.customDataSources.push(trimmedUrl)
      // 自动选中新添加的数据源
      this.selectedDataSources.push(trimmedUrl)

      this.$message.success('数据源添加成功')
      // 清空输入框，但保持表单显示，允许继续添加
      this.newSourceUrl = ''
    },

    // 删除自定义数据源
    removeCustomSource(index) {
      const sourceToRemove = this.customDataSources[index]
      // 从自定义数据源列表中移除
      this.customDataSources.splice(index, 1)
      // 从已选择列表中移除
      const selectedIndex = this.selectedDataSources.indexOf(sourceToRemove)
      if (selectedIndex > -1) {
        this.selectedDataSources.splice(selectedIndex, 1)
      }
      this.$message.success('数据源删除成功')
    },

    // 生成关联词
    generateRelatedWords() {
      // 检查是否填写了实体关键词和具体需求
      if (!this.entityKeyword.trim()) {
        this.$message.warning('请先填写实体关键词')
        return
      }

      if (!this.specificRequirement.trim()) {
        this.$message.warning('请先填写具体需求')
        return
      }

      // 这里可以调用API生成关联词
      this.$message.info('正在生成关联词...')

      // 模拟生成关联词的过程
      setTimeout(() => {
        // 根据实体关键词生成相关的关联词
        const generatedWords = [
          '老板电器 售后服务',
          '老板电器 三包义务',
          '老板电器 客服态度',
          '老板电器 质量',
          '老板电器 燃气灶爆炸',
          '老板电器 抽油烟机故障',
          '老板电器 投诉处理',
          '老板电器 对解',
          '老板电器 投诉公示',
          '老板电器 消费者不满',
          '老板电器 不满',
          '老板电器 投诉平台',
          '老板电器 虚假宣传',
          '老板电器 以次充好'
        ]

        // 保存所有生成的关键词
        this.generatedKeywords = [...generatedWords]

        // 默认选中前几个关键词（不超过最大数量）
        this.selectedKeywords = []
        generatedWords.forEach(word => {
          if (this.selectedKeywords.length < this.maxKeywords) {
            this.selectedKeywords.push(word)
          }
        })

        this.$message.success('关联词生成成功')
      }, 1000)
    },

    // 重新生成关联词
    regenerateKeywords() {
      // 直接调用生成关联词的方法
      this.generateRelatedWords()
    },

    // 处理定时推送按钮点击
    handleTimedPush() {
      this.timedTaskDialogVisible = true
    },

    // 关闭定时任务弹窗
    closeTimedTaskDialog() {
      this.timedTaskDialogVisible = false
    },

    // 处理创建定时任务
    handleCreateTimedTask() {
      this.createTaskDialogVisible = true
    },

    // 处理添加定时任务按钮
    handleAddTimedTask() {
      this.createTaskDialogVisible = true
    },

    // 关闭创建任务弹窗
    closeCreateTaskDialog() {
      this.createTaskDialogVisible = false
    },

    // 保存并运行任务
    saveAndRunTask() {
      this.$message.success('任务已保存并开始运行')
      this.createTaskDialogVisible = false
      // TODO: 实现保存并运行逻辑
    },

    // 保存任务计划
    saveTaskPlan() {
      this.$message.success('任务计划已保存')
      this.createTaskDialogVisible = false
      // TODO: 实现保存计划逻辑
    },

    // 修改计划
    modifyPlan() {
      this.$message.info('修改计划功能开发中...')
      // TODO: 实现修改计划逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.opinion-analysis {
  padding: 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

// 步骤指示器样式
.steps-container {
  background: white;
  padding: 20px 24px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left-actions {
    flex: 0 0 auto;

    .timed-push-btn {
      font-size: 14px;
      padding: 8px 16px;
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  .steps-wrapper {
    flex: 1;
    display: flex;
    justify-content: center;
    gap: 60px;
  }

  .right-placeholder {
    flex: 0 0 auto;
    width: 88px; // 与左侧按钮宽度保持平衡
  }

  .step-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #999;
    font-size: 14px;

    &.active {
      color: #5470c6;
      font-weight: 500;

      .step-number {
        background: #5470c6;
        color: white;
      }
    }

    .step-number {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #e8e8e8;
      color: #999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 500;
    }
  }
}

.main-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 24px;
}

// 分析来源区域
.analysis-source {
  background: white;
  border-radius: 8px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 32px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 24px 0;
  }
}

// 输入区域样式
.input-section {
  margin-bottom: 24px;

  .input-label {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;

    .required {
      color: #ff4d4f;
      margin-left: 2px;
    }

    .keyword-count {
      color: #999;
      font-weight: normal;
      margin-left: 8px;
      font-size: 13px;

      &.max-reached {
        color: #ff4d4f;
        font-weight: 500;
      }
    }
  }

  .entity-input {
    :deep(.el-input__inner) {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      padding: 12px 16px;
      font-size: 14px;

      &::placeholder {
        color: #bfbfbf;
      }

      &:focus {
        border-color: #5470c6;
        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
      }
    }

    &.error {
      :deep(.el-input__inner) {
        border-color: #ff4d4f;

        &:focus {
          border-color: #ff4d4f;
          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);
        }
      }
    }
  }

  .requirement-textarea {
    :deep(.el-textarea__inner) {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1.6;
      resize: vertical;

      &::placeholder {
        color: #bfbfbf;
      }

      &:focus {
        border-color: #5470c6;
        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
      }
    }

    &.error {
      :deep(.el-textarea__inner) {
        border-color: #ff4d4f;

        &:focus {
          border-color: #ff4d4f;
          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);
        }
      }
    }
  }
}

// 选择关联词区域
.related-words-section {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;

      .section-label {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }

      .word-count {
        font-size: 14px;
        color: #999;
        font-weight: normal;
        margin-left: 8px;
        transition: color 0.3s ease;

        &.max-reached {
          color: #ff4d4f;
          font-weight: 500;
        }
      }
    }

    .regenerate-btn {
      font-size: 13px;
      color: #5470c6;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f0f7ff;
        color: #4096ff;
      }

      i {
        margin-right: 4px;
        font-size: 12px;
      }
    }
  }

  .words-container {
    text-align: center;

    .generate-word-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      padding: 8px 16px;
      background: #f0f7ff;
      color: #5470c6;
      border: 1px dashed #5470c6;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 12px;

      &:hover {
        background: #e6f4ff;
        border-color: #4096ff;
      }

      i {
        font-size: 12px;
      }
    }

    .word-description {
      font-size: 12px;
      color: #999;
      line-height: 1.5;
    }
  }
}

// 关键词文本框包装器
.keywords-textbox-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fff;
  min-height: 120px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    border-color: #5470c6;
  }

  &:focus-within {
    border-color: #5470c6;
    box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
  }
}

// 生成的关键词显示区域
.generated-keywords-display {
  margin-bottom: 16px;

  .keyword-category {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .category-button {
      min-width: 100px;
      margin-right: 16px;
      margin-bottom: 8px;
      font-size: 13px;
      border-radius: 16px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .keyword-tags {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .keyword-tag {
        font-size: 13px;
        padding: 6px 12px;
        border-radius: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #d9d9d9;
        background: #fff;
        color: #666;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        user-select: none;

        &:hover {
          border-color: #5470c6;
          color: #5470c6;
        }

        &.selected {
          background: #5470c6;
          color: white;
          border-color: #5470c6;
        }
      }
    }
  }
}



// 关键词选择区域
.keywords-selection-section {
  .keywords-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .keyword-category {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    .category-label {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      min-width: 80px;
      padding-top: 6px;
    }

    .keyword-tags {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .keyword-tag {
        font-size: 13px;
        padding: 6px 12px;
        border-radius: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #d9d9d9;
        background: #fff;
        color: #666;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        user-select: none;

        &:hover {
          border-color: #5470c6;
          color: #5470c6;
        }

        &.selected {
          background: #5470c6;
          color: white;
          border-color: #5470c6;
        }

        &.highlight {
          background: #333;
          color: white;
          border-color: #333;
          position: relative;
          cursor: default;

          &::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid #333;
          }
        }
      }
    }
  }
}

// 第二步：数据概览样式
.data-overview {
  background: white;
  border-radius: 8px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 32px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 24px 0;
  }
}

.data-source-section {
  .source-option {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #5470c6;
      background: #f8f9fa;
    }

    .source-checkbox {
      :deep(.el-checkbox__input) {
        .el-checkbox__inner {
          width: 18px;
          height: 18px;
          border-radius: 4px;
          border: 2px solid #d9d9d9;

          &::after {
            width: 5px;
            height: 9px;
            left: 5px;
            top: 1px;
          }
        }

        &.is-checked .el-checkbox__inner {
          background-color: #5470c6;
          border-color: #5470c6;
        }
      }
    }

    .source-icon {
      width: 40px;
      height: 40px;
      background: #f0f7ff;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        font-size: 20px;
        color: #5470c6;
      }
    }

    .source-content {
      flex: 1;

      h3 {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin: 0;
        word-break: break-all;
        line-height: 1.4;
      }
    }

    .source-actions {
      display: flex;
      align-items: center;

      .el-icon-delete {
        font-size: 16px;
        color: #999;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: #ff4d4f;
        }
      }
    }
  }

  .add-source-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    color: #999;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;

    &:hover {
      border-color: #5470c6;
      color: #5470c6;
      background: #f8f9fa;
    }

    i {
      font-size: 16px;
    }
  }
}

// 底部按钮区域
.bottom-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding-top: 24px;

  .el-button {
    padding: 12px 32px;
    font-size: 16px;

    &:disabled {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
      color: #bfbfbf;
      cursor: not-allowed;
    }
  }
}

// 新增数据源表单样式
.add-source-form {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  margin-top: 16px;

  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }

    .el-icon-close {
      font-size: 18px;
      color: #999;
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #666;
      }
    }
  }

  .form-item {
    .form-label {
      display: block;
      font-size: 14px;
      color: #333;
      font-weight: 500;
      margin-bottom: 8px;

      .required {
        color: #ff4d4f;
        margin-left: 2px;
      }
    }

    .input-group {
      display: flex;
      gap: 12px;
      align-items: flex-start;

      .source-url-input {
        flex: 1;

        :deep(.el-input__inner) {
          border-radius: 6px;
          border: 1px solid #d9d9d9;
          padding: 12px 16px;
          font-size: 14px;

          &::placeholder {
            color: #bfbfbf;
          }

          &:focus {
            border-color: #5470c6;
            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
          }
        }
      }

      .el-button {
        padding: 12px 24px;
        font-size: 14px;
        border-radius: 6px;
        white-space: nowrap;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .steps-container {
    padding: 16px 12px;
    flex-direction: column;
    gap: 16px;

    .left-actions {
      align-self: flex-start;

      .timed-push-btn {
        font-size: 13px;
        padding: 6px 12px;
      }
    }

    .steps-wrapper {
      gap: 30px;
    }

    .right-placeholder {
      display: none;
    }

    .step-item {
      font-size: 13px;
    }
  }

  .main-content {
    padding: 24px 16px;
  }

  .analysis-source {
    padding: 24px 20px;
  }

  .document-content {
    padding: 12px;
    min-height: 100px;
  }
}

// 定时任务抽屉样式
.timed-task-drawer {
  :deep(.el-drawer__header) {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 0;
  }

  :deep(.el-drawer__body) {
    padding: 0;
  }
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .drawer-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .add-task-btn {
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 4px;

    .el-icon-plus {
      margin-right: 4px;
    }
  }
}

.drawer-content {
  padding: 24px;
  min-height: 400px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.empty-content {
  text-align: center;

  .empty-icon {
    margin-bottom: 24px;
    display: flex;
    justify-content: center;

    svg {
      opacity: 0.6;
    }
  }

  .empty-text {
    font-size: 16px;
    color: #909399;
    margin: 0 0 24px 0;
    font-weight: 500;
  }

  .create-btn {
    padding: 10px 24px;
    font-size: 14px;
    border-radius: 6px;
    font-weight: 500;
  }
}

// 创建任务弹窗样式
.create-task-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
  }

  :deep(.el-dialog__header) {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f0f0;
  }
}

.task-form {
  .task-content {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 24px;

    .task-icon {
      width: 48px;
      height: 48px;
      background: #5470c6;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        font-size: 24px;
        color: white;
      }
    }

    .task-info {
      flex: 1;

      .task-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 4px 0;
      }

      .task-description {
        font-size: 14px;
        color: #666;
        margin: 0;
        line-height: 1.4;
      }
    }
  }

  .execute-time-section {
    .section-label {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
    }

    .time-selector {
      display: flex;
      gap: 12px;
      align-items: center;

      .frequency-select {
        width: 120px;
      }

      .time-picker {
        width: 140px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .modify-btn {
    color: #666;
    border-color: #d9d9d9;

    &:hover {
      color: #5470c6;
      border-color: #5470c6;
    }
  }

  .run-btn {
    background: #5470c6;
    border-color: #5470c6;

    &:hover {
      background: #4096ff;
      border-color: #4096ff;
    }
  }

  .save-btn {
    background: #52c41a;
    border-color: #52c41a;

    &:hover {
      background: #73d13d;
      border-color: #73d13d;
    }
  }
}

// 任务列表样式（待实现）
// .task-list {
//   // TODO: 任务列表样式
// }
</style>
