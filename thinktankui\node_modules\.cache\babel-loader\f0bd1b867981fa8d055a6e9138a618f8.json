{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751529688975}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\babel.config.js", "mtime": 1750933247176}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "currentStep", "entityKeyword", "specificRequirement", "selectedKeywords", "generatedKeywords", "maxKeywords", "selectedDataSources", "customDataSources", "showAddSourceInput", "newSourceUrl", "showValidation", "timedTaskDialogVisible", "timedTaskList", "computed", "canGoToNextStep", "trim", "length", "groupedKeywords", "categories", "keywords", "for<PERSON>ach", "keyword", "assigned", "cat", "includes", "push", "find", "result", "mounted", "console", "log", "methods", "toggleKeyword", "index", "indexOf", "splice", "$message", "warning", "concat", "isKeywordSelected", "toggleCategorySelection", "categoryName", "categoryKeywords", "_this", "allSelected", "every", "info", "notSelected", "filter", "success", "goToNextStep", "goToPreviousStep", "toggleDataSource", "source", "showAddSourceForm", "hideAddSourceForm", "confirmAddSource", "urlPattern", "test", "trimmedUrl", "removeCustomSource", "sourceToRemove", "selectedIndex", "generateRelatedWords", "_this2", "setTimeout", "generatedWords", "word", "regenerateKeywords", "handleTimedPush", "closeTimedTaskDialog", "handleCreateTimedTask", "handleAddTimedTask"], "sources": ["src/views/opinion-analysis/index.vue"], "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 步骤指示器 -->\n    <div class=\"steps-container\">\n      <!-- 左侧按钮区域 -->\n      <div class=\"left-actions\">\n        <el-button\n          class=\"timed-push-btn\"\n          type=\"primary\"\n          size=\"small\"\n          @click=\"handleTimedPush\"\n        >\n          定时推送\n        </el-button>\n      </div>\n\n      <!-- 步骤指示器 -->\n      <div class=\"steps-wrapper\">\n        <div class=\"step-item\" :class=\"{ active: currentStep === 1 }\">\n          <span class=\"step-number\">1</span>\n          <span class=\"step-text\">舆情分析来源</span>\n        </div>\n        <div class=\"step-item\" :class=\"{ active: currentStep === 2 }\">\n          <span class=\"step-number\">2</span>\n          <span class=\"step-text\">数据概览</span>\n        </div>\n      </div>\n\n      <!-- 右侧占位区域，保持布局平衡 -->\n      <div class=\"right-placeholder\"></div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 第一步：分析需求 -->\n      <div v-if=\"currentStep === 1\" class=\"analysis-source\">\n        <h2 class=\"section-title\">分析需求</h2>\n\n        <!-- 实体关键词区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            实体关键词\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"entityKeyword\"\n            placeholder=\"请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等\"\n            class=\"entity-input\"\n            :class=\"{ 'error': !entityKeyword.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 具体需求区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            具体需求\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"specificRequirement\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）\"\n            class=\"requirement-textarea\"\n            :class=\"{ 'error': !specificRequirement.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 选择关联词区域 -->\n        <div class=\"related-words-section\">\n          <div class=\"section-header\">\n            <div class=\"header-left\">\n              <span class=\"section-label\">选择关联词</span>\n              <span class=\"word-count\" :class=\"{ 'max-reached': selectedKeywords.length >= maxKeywords }\">\n                ({{ selectedKeywords.length }}/{{ maxKeywords }})\n              </span>\n            </div>\n            <el-button\n              v-if=\"generatedKeywords.length > 0\"\n              class=\"regenerate-btn\"\n              size=\"mini\"\n              type=\"text\"\n              @click=\"regenerateKeywords\"\n            >\n              <i class=\"el-icon-refresh\"></i>\n              重新生成\n            </el-button>\n          </div>\n\n          <div class=\"keywords-textbox-wrapper\">\n            <!-- 显示生成的关键词 -->\n            <div v-if=\"generatedKeywords.length > 0\" class=\"generated-keywords-display\">\n              <div v-for=\"(category, categoryName) in groupedKeywords\" :key=\"categoryName\" class=\"keyword-category\">\n                <el-button\n                  class=\"category-button\"\n                  size=\"small\"\n                  type=\"primary\"\n                  plain\n                  @click=\"toggleCategorySelection(categoryName, category)\"\n                >\n                  {{ categoryName }}\n                </el-button>\n                <div class=\"keyword-tags\">\n                  <el-tag\n                    v-for=\"(keyword, index) in category\"\n                    :key=\"index\"\n                    :class=\"['keyword-tag', { selected: isKeywordSelected(keyword) }]\"\n                    @click=\"toggleKeyword(keyword)\"\n                  >\n                    {{ keyword }}\n                  </el-tag>\n                </div>\n              </div>\n            </div>\n\n            <!-- 生成关联词按钮区域 -->\n            <div v-if=\"generatedKeywords.length === 0\" class=\"words-container\">\n              <div class=\"generate-word-btn\" @click=\"generateRelatedWords\">\n                <i class=\"el-icon-magic-stick\"></i>\n                <span>生成关联词</span>\n              </div>\n              <div class=\"word-description\">\n                根据你填写的需求和关键词生成关联词\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第二步：数据概览 -->\n      <div v-if=\"currentStep === 2\" class=\"data-overview\">\n        <h2 class=\"section-title\">选择数据来源</h2>\n\n        <!-- 数据来源选项 -->\n        <div class=\"data-source-section\">\n          <div class=\"source-option\" @click=\"toggleDataSource('online-search')\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"'online-search'\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-search\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>联网搜索</h3>\n            </div>\n          </div>\n\n          <!-- 自定义数据源列表 -->\n          <div v-for=\"(source, index) in customDataSources\" :key=\"index\" class=\"source-option\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"source\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-link\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>{{ source }}</h3>\n            </div>\n            <div class=\"source-actions\">\n              <i class=\"el-icon-delete\" @click=\"removeCustomSource(index)\"></i>\n            </div>\n          </div>\n\n          <!-- 新增数据源表单 -->\n          <div v-if=\"showAddSourceInput\" class=\"add-source-form\">\n            <div class=\"form-header\">\n              <h3>新增数据源</h3>\n              <i class=\"el-icon-close\" @click=\"hideAddSourceForm\"></i>\n            </div>\n            <div class=\"form-item\">\n              <label class=\"form-label\">\n                数据源网址\n                <span class=\"required\">*</span>\n              </label>\n              <div class=\"input-group\">\n                <el-input\n                  v-model=\"newSourceUrl\"\n                  placeholder=\"请输入网址，例如：https://www.example.com\"\n                  class=\"source-url-input\"\n                  @keyup.enter=\"confirmAddSource\"\n                />\n                <el-button type=\"primary\" @click=\"confirmAddSource\">确定</el-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- 新增来源按钮 -->\n          <div v-if=\"!showAddSourceInput\" class=\"add-source-btn\" @click=\"showAddSourceForm\">\n            <i class=\"el-icon-plus\"></i>\n            <span>新增来源</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 底部按钮区域 -->\n      <div class=\"bottom-actions\">\n        <el-button v-if=\"currentStep === 2\" @click=\"goToPreviousStep\" size=\"large\">上一步</el-button>\n        <el-button\n          v-if=\"currentStep === 1\"\n          @click=\"goToNextStep\"\n          type=\"primary\"\n          size=\"large\"\n          :disabled=\"!canGoToNextStep\"\n        >下一步</el-button>\n        <el-button v-if=\"currentStep === 2\" type=\"primary\" size=\"large\">开始分析</el-button>\n      </div>\n    </div>\n\n    <!-- 定时任务弹窗 -->\n    <el-dialog\n      title=\"定时任务\"\n      :visible.sync=\"timedTaskDialogVisible\"\n      width=\"600px\"\n      :before-close=\"closeTimedTaskDialog\"\n      class=\"timed-task-dialog\"\n    >\n      <!-- 弹窗头部右侧按钮 -->\n      <div slot=\"title\" class=\"dialog-header\">\n        <span class=\"dialog-title\">定时任务</span>\n        <el-button\n          type=\"primary\"\n          size=\"mini\"\n          icon=\"el-icon-plus\"\n          @click=\"handleAddTimedTask\"\n          class=\"add-task-btn\"\n        >\n          定时任务\n        </el-button>\n      </div>\n\n      <!-- 弹窗内容 -->\n      <div class=\"dialog-content\">\n        <!-- 空状态 -->\n        <div v-if=\"timedTaskList.length === 0\" class=\"empty-state\">\n          <div class=\"empty-content\">\n            <!-- 空状态图标 -->\n            <div class=\"empty-icon\">\n              <svg width=\"120\" height=\"120\" viewBox=\"0 0 120 120\" fill=\"none\">\n                <!-- 文件夹图标 -->\n                <path d=\"M20 30h25l5-10h50v70H20V30z\" fill=\"#f0f0f0\" stroke=\"#d0d0d0\" stroke-width=\"2\"/>\n                <path d=\"M25 35h70v50H25V35z\" fill=\"#fafafa\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <!-- 文档图标 -->\n                <rect x=\"35\" y=\"45\" width=\"30\" height=\"25\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\"/>\n                <rect x=\"70\" y=\"50\" width=\"20\" height=\"15\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\"/>\n                <!-- 装饰线条 -->\n                <line x1=\"40\" y1=\"52\" x2=\"60\" y2=\"52\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"40\" y1=\"57\" x2=\"55\" y2=\"57\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"40\" y1=\"62\" x2=\"58\" y2=\"62\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"75\" y1=\"55\" x2=\"85\" y2=\"55\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"75\" y1=\"60\" x2=\"82\" y2=\"60\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n              </svg>\n            </div>\n            <p class=\"empty-text\">暂无定时任务</p>\n            <el-button type=\"primary\" @click=\"handleCreateTimedTask\" class=\"create-btn\">\n              去创建\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 任务列表 -->\n        <div v-else class=\"task-list\">\n          <!-- TODO: 这里将来显示定时任务列表 -->\n        </div>\n      </div>\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      currentStep: 1, // 当前步骤\n      entityKeyword: '', // 实体关键词\n      specificRequirement: '', // 具体需求\n      selectedKeywords: [], // 已选择的关键词\n      generatedKeywords: [], // 生成的所有关键词\n      maxKeywords: 5, // 最大选择数量\n      selectedDataSources: ['online-search'], // 已选择的数据来源\n      customDataSources: [], // 自定义数据源列表\n      showAddSourceInput: false, // 显示新增数据源表单\n      newSourceUrl: '', // 新增数据源URL\n      showValidation: false, // 是否显示验证错误样式\n      timedTaskDialogVisible: false, // 定时任务弹窗显示状态\n      timedTaskList: [] // 定时任务列表\n    }\n  },\n  computed: {\n    // 检查是否可以进入下一步\n    canGoToNextStep() {\n      // 检查实体关键词是否填写\n      if (!this.entityKeyword.trim()) {\n        return false\n      }\n\n      // 检查具体需求是否填写\n      if (!this.specificRequirement.trim()) {\n        return false\n      }\n\n      // 检查是否至少选择了一个关键词\n      if (this.selectedKeywords.length === 0) {\n        return false\n      }\n\n      return true\n    },\n\n    // 将关键词按分类分组\n    groupedKeywords() {\n      if (this.generatedKeywords.length === 0) {\n        return {}\n      }\n\n      // 动态生成分类按钮\n      const categories = [\n        { name: '售后服务问题', keywords: [] },\n        { name: '产品质量问题', keywords: [] },\n        { name: '投诉处理结果', keywords: [] },\n        { name: '消费者不满', keywords: [] },\n        { name: '虚假宣传', keywords: [] }\n      ]\n\n      this.generatedKeywords.forEach(keyword => {\n        let assigned = false\n\n        categories.forEach(cat => {\n          if (cat.name === '售后服务问题' && (keyword.includes('售后') || keyword.includes('服务') || keyword.includes('客服'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '产品质量问题' && (keyword.includes('质量') || keyword.includes('爆炸') || keyword.includes('故障'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '投诉处理结果' && (keyword.includes('投诉') || keyword.includes('处理') || keyword.includes('对解'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '消费者不满' && (keyword.includes('不满') || keyword.includes('消费者'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '虚假宣传' && (keyword.includes('宣传') || keyword.includes('充好'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          }\n        })\n\n        if (!assigned) {\n          // 如果没有匹配的分类，添加到第一个有关键词的分类或创建新分类\n          if (categories[0].keywords.length === 0) {\n            categories[0].keywords.push(keyword)\n          } else {\n            categories.find(cat => cat.keywords.length > 0).keywords.push(keyword)\n          }\n        }\n      })\n\n      // 只返回有关键词的分类\n      const result = {}\n      categories.forEach(cat => {\n        if (cat.keywords.length > 0) {\n          result[cat.name] = cat.keywords\n        }\n      })\n\n      return result\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 切换关键词选择状态\n    toggleKeyword(keyword) {\n      const index = this.selectedKeywords.indexOf(keyword)\n      if (index > -1) {\n        // 如果已选中，则取消选择\n        this.selectedKeywords.splice(index, 1)\n      } else {\n        // 如果未选中，检查是否超过最大数量\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(keyword)\n        } else {\n          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)\n        }\n      }\n    },\n\n    // 检查关键词是否已选中\n    isKeywordSelected(keyword) {\n      return this.selectedKeywords.includes(keyword)\n    },\n\n    // 切换分类选择状态\n    toggleCategorySelection(categoryName, categoryKeywords) {\n      // 检查该分类下的所有关键词是否都已选中\n      const allSelected = categoryKeywords.every(keyword => this.isKeywordSelected(keyword))\n\n      if (allSelected) {\n        // 如果都已选中，则取消选择该分类下的所有关键词\n        categoryKeywords.forEach(keyword => {\n          const index = this.selectedKeywords.indexOf(keyword)\n          if (index > -1) {\n            this.selectedKeywords.splice(index, 1)\n          }\n        })\n        this.$message.info(`已取消选择\"${categoryName}\"分类下的所有关键词`)\n      } else {\n        // 如果没有全部选中，则选择该分类下的所有关键词\n        categoryKeywords.forEach(keyword => {\n          if (!this.isKeywordSelected(keyword) && this.selectedKeywords.length < this.maxKeywords) {\n            this.selectedKeywords.push(keyword)\n          }\n        })\n\n        // 检查是否因为数量限制而无法全部选择\n        const notSelected = categoryKeywords.filter(keyword => !this.isKeywordSelected(keyword))\n        if (notSelected.length > 0) {\n          this.$message.warning(`由于数量限制，无法选择\"${categoryName}\"分类下的所有关键词`)\n        } else {\n          this.$message.success(`已选择\"${categoryName}\"分类下的所有关键词`)\n        }\n      }\n    },\n\n\n    // 前往下一步\n    goToNextStep() {\n      // 显示验证样式\n      this.showValidation = true\n\n      // 验证表单是否填写完整\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请填写具体需求')\n        return\n      }\n\n      if (this.selectedKeywords.length === 0) {\n        this.$message.warning('请至少选择一个关键词')\n        return\n      }\n\n      // 验证通过，隐藏验证样式并进入下一步\n      this.showValidation = false\n      if (this.currentStep < 2) {\n        this.currentStep++\n      }\n    },\n\n    // 返回上一步\n    goToPreviousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--\n      }\n    },\n\n    // 切换数据来源选择\n    toggleDataSource(source) {\n      const index = this.selectedDataSources.indexOf(source)\n      if (index > -1) {\n        this.selectedDataSources.splice(index, 1)\n      } else {\n        this.selectedDataSources.push(source)\n      }\n    },\n\n    // 显示新增数据源表单\n    showAddSourceForm() {\n      this.showAddSourceInput = true\n      this.newSourceUrl = ''\n    },\n\n    // 隐藏新增数据源表单\n    hideAddSourceForm() {\n      this.showAddSourceInput = false\n      this.newSourceUrl = ''\n    },\n\n    // 确认新增数据源\n    confirmAddSource() {\n      if (!this.newSourceUrl.trim()) {\n        this.$message.warning('请输入数据源网址')\n        return\n      }\n\n      // 简单的URL格式验证\n      const urlPattern = /^https?:\\/\\/.+/\n      if (!urlPattern.test(this.newSourceUrl.trim())) {\n        this.$message.warning('请输入有效的网址格式')\n        return\n      }\n\n      // 检查是否已存在相同的数据源\n      const trimmedUrl = this.newSourceUrl.trim()\n      if (this.customDataSources.includes(trimmedUrl)) {\n        this.$message.warning('该数据源已存在')\n        return\n      }\n\n      // 将新的数据源添加到自定义数据源列表中\n      this.customDataSources.push(trimmedUrl)\n      // 自动选中新添加的数据源\n      this.selectedDataSources.push(trimmedUrl)\n\n      this.$message.success('数据源添加成功')\n      // 清空输入框，但保持表单显示，允许继续添加\n      this.newSourceUrl = ''\n    },\n\n    // 删除自定义数据源\n    removeCustomSource(index) {\n      const sourceToRemove = this.customDataSources[index]\n      // 从自定义数据源列表中移除\n      this.customDataSources.splice(index, 1)\n      // 从已选择列表中移除\n      const selectedIndex = this.selectedDataSources.indexOf(sourceToRemove)\n      if (selectedIndex > -1) {\n        this.selectedDataSources.splice(selectedIndex, 1)\n      }\n      this.$message.success('数据源删除成功')\n    },\n\n    // 生成关联词\n    generateRelatedWords() {\n      // 检查是否填写了实体关键词和具体需求\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请先填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请先填写具体需求')\n        return\n      }\n\n      // 这里可以调用API生成关联词\n      this.$message.info('正在生成关联词...')\n\n      // 模拟生成关联词的过程\n      setTimeout(() => {\n        // 根据实体关键词生成相关的关联词\n        const generatedWords = [\n          '老板电器 售后服务',\n          '老板电器 三包义务',\n          '老板电器 客服态度',\n          '老板电器 质量',\n          '老板电器 燃气灶爆炸',\n          '老板电器 抽油烟机故障',\n          '老板电器 投诉处理',\n          '老板电器 对解',\n          '老板电器 投诉公示',\n          '老板电器 消费者不满',\n          '老板电器 不满',\n          '老板电器 投诉平台',\n          '老板电器 虚假宣传',\n          '老板电器 以次充好'\n        ]\n\n        // 保存所有生成的关键词\n        this.generatedKeywords = [...generatedWords]\n\n        // 默认选中前几个关键词（不超过最大数量）\n        this.selectedKeywords = []\n        generatedWords.forEach(word => {\n          if (this.selectedKeywords.length < this.maxKeywords) {\n            this.selectedKeywords.push(word)\n          }\n        })\n\n        this.$message.success('关联词生成成功')\n      }, 1000)\n    },\n\n    // 重新生成关联词\n    regenerateKeywords() {\n      // 直接调用生成关联词的方法\n      this.generateRelatedWords()\n    },\n\n    // 处理定时推送按钮点击\n    handleTimedPush() {\n      this.timedTaskDialogVisible = true\n    },\n\n    // 关闭定时任务弹窗\n    closeTimedTaskDialog() {\n      this.timedTaskDialogVisible = false\n    },\n\n    // 处理创建定时任务\n    handleCreateTimedTask() {\n      this.$message.info('创建定时任务功能开发中...')\n      // TODO: 实现创建定时任务功能\n    },\n\n    // 处理添加定时任务按钮\n    handleAddTimedTask() {\n      this.$message.info('添加定时任务功能开发中...')\n      // TODO: 实现添加定时任务功能\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n// 步骤指示器样式\n.steps-container {\n  background: white;\n  padding: 20px 24px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  .left-actions {\n    flex: 0 0 auto;\n\n    .timed-push-btn {\n      font-size: 14px;\n      padding: 8px 16px;\n      border-radius: 6px;\n      font-weight: 500;\n      transition: all 0.3s ease;\n\n      &:hover {\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    }\n  }\n\n  .steps-wrapper {\n    flex: 1;\n    display: flex;\n    justify-content: center;\n    gap: 60px;\n  }\n\n  .right-placeholder {\n    flex: 0 0 auto;\n    width: 88px; // 与左侧按钮宽度保持平衡\n  }\n\n  .step-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: #999;\n    font-size: 14px;\n\n    &.active {\n      color: #5470c6;\n      font-weight: 500;\n\n      .step-number {\n        background: #5470c6;\n        color: white;\n      }\n    }\n\n    .step-number {\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      background: #e8e8e8;\n      color: #999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      font-weight: 500;\n    }\n  }\n}\n\n.main-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 40px 24px;\n}\n\n// 分析来源区域\n.analysis-source {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 输入区域样式\n.input-section {\n  margin-bottom: 24px;\n\n  .input-label {\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n\n    .required {\n      color: #ff4d4f;\n      margin-left: 2px;\n    }\n\n    .keyword-count {\n      color: #999;\n      font-weight: normal;\n      margin-left: 8px;\n      font-size: 13px;\n\n      &.max-reached {\n        color: #ff4d4f;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .entity-input {\n    :deep(.el-input__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-input__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n\n  .requirement-textarea {\n    :deep(.el-textarea__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n      line-height: 1.6;\n      resize: vertical;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-textarea__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n}\n\n// 选择关联词区域\n.related-words-section {\n  .section-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 16px;\n\n    .header-left {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .section-label {\n        font-size: 14px;\n        color: #333;\n        font-weight: 500;\n      }\n\n      .word-count {\n        font-size: 14px;\n        color: #999;\n        font-weight: normal;\n        margin-left: 8px;\n        transition: color 0.3s ease;\n\n        &.max-reached {\n          color: #ff4d4f;\n          font-weight: 500;\n        }\n      }\n    }\n\n    .regenerate-btn {\n      font-size: 13px;\n      color: #5470c6;\n      padding: 4px 8px;\n      border-radius: 4px;\n      transition: all 0.3s ease;\n\n      &:hover {\n        background-color: #f0f7ff;\n        color: #4096ff;\n      }\n\n      i {\n        margin-right: 4px;\n        font-size: 12px;\n      }\n    }\n  }\n\n  .words-container {\n    text-align: center;\n\n    .generate-word-btn {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      padding: 8px 16px;\n      background: #f0f7ff;\n      color: #5470c6;\n      border: 1px dashed #5470c6;\n      border-radius: 4px;\n      font-size: 14px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n\n      &:hover {\n        background: #e6f4ff;\n        border-color: #4096ff;\n      }\n\n      i {\n        font-size: 12px;\n      }\n    }\n\n    .word-description {\n      font-size: 12px;\n      color: #999;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 关键词文本框包装器\n.keywords-textbox-wrapper {\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  padding: 16px;\n  background: #fff;\n  min-height: 120px;\n  transition: border-color 0.3s ease, box-shadow 0.3s ease;\n\n  &:hover {\n    border-color: #5470c6;\n  }\n\n  &:focus-within {\n    border-color: #5470c6;\n    box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n  }\n}\n\n// 生成的关键词显示区域\n.generated-keywords-display {\n  margin-bottom: 16px;\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n    margin-bottom: 16px;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .category-button {\n      min-width: 100px;\n      margin-right: 16px;\n      margin-bottom: 8px;\n      font-size: 13px;\n      border-radius: 16px;\n      transition: all 0.3s ease;\n\n      &:hover {\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n      }\n    }\n  }\n}\n\n\n\n// 关键词选择区域\n.keywords-selection-section {\n  .keywords-grid {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n  }\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n\n    .category-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      min-width: 80px;\n      padding-top: 6px;\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n\n        &.highlight {\n          background: #333;\n          color: white;\n          border-color: #333;\n          position: relative;\n          cursor: default;\n\n          &::after {\n            content: '';\n            position: absolute;\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 0;\n            height: 0;\n            border-left: 6px solid transparent;\n            border-right: 6px solid transparent;\n            border-top: 6px solid #333;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 第二步：数据概览样式\n.data-overview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n.data-source-section {\n  .source-option {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    padding: 16px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    .source-checkbox {\n      :deep(.el-checkbox__input) {\n        .el-checkbox__inner {\n          width: 18px;\n          height: 18px;\n          border-radius: 4px;\n          border: 2px solid #d9d9d9;\n\n          &::after {\n            width: 5px;\n            height: 9px;\n            left: 5px;\n            top: 1px;\n          }\n        }\n\n        &.is-checked .el-checkbox__inner {\n          background-color: #5470c6;\n          border-color: #5470c6;\n        }\n      }\n    }\n\n    .source-icon {\n      width: 40px;\n      height: 40px;\n      background: #f0f7ff;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      i {\n        font-size: 20px;\n        color: #5470c6;\n      }\n    }\n\n    .source-content {\n      flex: 1;\n\n      h3 {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n        margin: 0;\n        word-break: break-all;\n        line-height: 1.4;\n      }\n    }\n\n    .source-actions {\n      display: flex;\n      align-items: center;\n\n      .el-icon-delete {\n        font-size: 16px;\n        color: #999;\n        cursor: pointer;\n        transition: color 0.3s ease;\n\n        &:hover {\n          color: #ff4d4f;\n        }\n      }\n    }\n  }\n\n  .add-source-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    padding: 16px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    color: #999;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n\n    &:hover {\n      border-color: #5470c6;\n      color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    i {\n      font-size: 16px;\n    }\n  }\n}\n\n// 底部按钮区域\n.bottom-actions {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 16px;\n  padding-top: 24px;\n\n  .el-button {\n    padding: 12px 32px;\n    font-size: 16px;\n\n    &:disabled {\n      background-color: #f5f5f5;\n      border-color: #d9d9d9;\n      color: #bfbfbf;\n      cursor: not-allowed;\n    }\n  }\n}\n\n// 新增数据源表单样式\n.add-source-form {\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 8px;\n  padding: 20px;\n  margin-top: 16px;\n\n  .form-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n    padding-bottom: 12px;\n    border-bottom: 1px solid #f0f0f0;\n\n    h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .el-icon-close {\n      font-size: 18px;\n      color: #999;\n      cursor: pointer;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: #666;\n      }\n    }\n  }\n\n  .form-item {\n    .form-label {\n      display: block;\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      margin-bottom: 8px;\n\n      .required {\n        color: #ff4d4f;\n        margin-left: 2px;\n      }\n    }\n\n    .input-group {\n      display: flex;\n      gap: 12px;\n      align-items: flex-start;\n\n      .source-url-input {\n        flex: 1;\n\n        :deep(.el-input__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 12px 16px;\n          font-size: 14px;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n\n      .el-button {\n        padding: 12px 24px;\n        font-size: 14px;\n        border-radius: 6px;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .steps-container {\n    padding: 16px 12px;\n    flex-direction: column;\n    gap: 16px;\n\n    .left-actions {\n      align-self: flex-start;\n\n      .timed-push-btn {\n        font-size: 13px;\n        padding: 6px 12px;\n      }\n    }\n\n    .steps-wrapper {\n      gap: 30px;\n    }\n\n    .right-placeholder {\n      display: none;\n    }\n\n    .step-item {\n      font-size: 13px;\n    }\n  }\n\n  .main-content {\n    padding: 24px 16px;\n  }\n\n  .analysis-source {\n    padding: 24px 20px;\n  }\n\n  .document-content {\n    padding: 12px;\n    min-height: 100px;\n  }\n}\n\n// 定时任务弹窗样式\n.timed-task-dialog {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n\n    .el-dialog__header {\n      padding: 20px 24px 16px;\n      border-bottom: 1px solid #f0f0f0;\n    }\n\n    .el-dialog__body {\n      padding: 0;\n    }\n  }\n}\n\n.dialog-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n\n  .dialog-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n  }\n\n  .add-task-btn {\n    font-size: 14px;\n    padding: 6px 12px;\n    border-radius: 4px;\n\n    .el-icon-plus {\n      margin-right: 4px;\n    }\n  }\n}\n\n.dialog-content {\n  padding: 24px;\n  min-height: 400px;\n}\n\n.empty-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400px;\n}\n\n.empty-content {\n  text-align: center;\n\n  .empty-icon {\n    margin-bottom: 24px;\n    display: flex;\n    justify-content: center;\n\n    svg {\n      opacity: 0.6;\n    }\n  }\n\n  .empty-text {\n    font-size: 16px;\n    color: #909399;\n    margin: 0 0 24px 0;\n    font-weight: 500;\n  }\n\n  .create-btn {\n    padding: 10px 24px;\n    font-size: 14px;\n    border-radius: 6px;\n    font-weight: 500;\n  }\n}\n\n// 任务列表样式（待实现）\n// .task-list {\n//   // TODO: 任务列表样式\n// }\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;iCAkRA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MAAA;MACAC,aAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,WAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;MAAA;MACAC,sBAAA;MAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA;MACA,UAAAb,aAAA,CAAAc,IAAA;QACA;MACA;;MAEA;MACA,UAAAb,mBAAA,CAAAa,IAAA;QACA;MACA;;MAEA;MACA,SAAAZ,gBAAA,CAAAa,MAAA;QACA;MACA;MAEA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAA;MACA,SAAAb,iBAAA,CAAAY,MAAA;QACA;MACA;;MAEA;MACA,IAAAE,UAAA,IACA;QAAApB,IAAA;QAAAqB,QAAA;MAAA,GACA;QAAArB,IAAA;QAAAqB,QAAA;MAAA,GACA;QAAArB,IAAA;QAAAqB,QAAA;MAAA,GACA;QAAArB,IAAA;QAAAqB,QAAA;MAAA,GACA;QAAArB,IAAA;QAAAqB,QAAA;MAAA,EACA;MAEA,KAAAf,iBAAA,CAAAgB,OAAA,WAAAC,OAAA;QACA,IAAAC,QAAA;QAEAJ,UAAA,CAAAE,OAAA,WAAAG,GAAA;UACA,IAAAA,GAAA,CAAAzB,IAAA,kBAAAuB,OAAA,CAAAG,QAAA,UAAAH,OAAA,CAAAG,QAAA,UAAAH,OAAA,CAAAG,QAAA;YACAD,GAAA,CAAAJ,QAAA,CAAAM,IAAA,CAAAJ,OAAA;YACAC,QAAA;UACA,WAAAC,GAAA,CAAAzB,IAAA,kBAAAuB,OAAA,CAAAG,QAAA,UAAAH,OAAA,CAAAG,QAAA,UAAAH,OAAA,CAAAG,QAAA;YACAD,GAAA,CAAAJ,QAAA,CAAAM,IAAA,CAAAJ,OAAA;YACAC,QAAA;UACA,WAAAC,GAAA,CAAAzB,IAAA,kBAAAuB,OAAA,CAAAG,QAAA,UAAAH,OAAA,CAAAG,QAAA,UAAAH,OAAA,CAAAG,QAAA;YACAD,GAAA,CAAAJ,QAAA,CAAAM,IAAA,CAAAJ,OAAA;YACAC,QAAA;UACA,WAAAC,GAAA,CAAAzB,IAAA,iBAAAuB,OAAA,CAAAG,QAAA,UAAAH,OAAA,CAAAG,QAAA;YACAD,GAAA,CAAAJ,QAAA,CAAAM,IAAA,CAAAJ,OAAA;YACAC,QAAA;UACA,WAAAC,GAAA,CAAAzB,IAAA,gBAAAuB,OAAA,CAAAG,QAAA,UAAAH,OAAA,CAAAG,QAAA;YACAD,GAAA,CAAAJ,QAAA,CAAAM,IAAA,CAAAJ,OAAA;YACAC,QAAA;UACA;QACA;QAEA,KAAAA,QAAA;UACA;UACA,IAAAJ,UAAA,IAAAC,QAAA,CAAAH,MAAA;YACAE,UAAA,IAAAC,QAAA,CAAAM,IAAA,CAAAJ,OAAA;UACA;YACAH,UAAA,CAAAQ,IAAA,WAAAH,GAAA;cAAA,OAAAA,GAAA,CAAAJ,QAAA,CAAAH,MAAA;YAAA,GAAAG,QAAA,CAAAM,IAAA,CAAAJ,OAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAM,MAAA;MACAT,UAAA,CAAAE,OAAA,WAAAG,GAAA;QACA,IAAAA,GAAA,CAAAJ,QAAA,CAAAH,MAAA;UACAW,MAAA,CAAAJ,GAAA,CAAAzB,IAAA,IAAAyB,GAAA,CAAAJ,QAAA;QACA;MACA;MAEA,OAAAQ,MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACAC,OAAA,CAAAC,GAAA;EACA;EACAC,OAAA;IACA;IACAC,aAAA,WAAAA,cAAAX,OAAA;MACA,IAAAY,KAAA,QAAA9B,gBAAA,CAAA+B,OAAA,CAAAb,OAAA;MACA,IAAAY,KAAA;QACA;QACA,KAAA9B,gBAAA,CAAAgC,MAAA,CAAAF,KAAA;MACA;QACA;QACA,SAAA9B,gBAAA,CAAAa,MAAA,QAAAX,WAAA;UACA,KAAAF,gBAAA,CAAAsB,IAAA,CAAAJ,OAAA;QACA;UACA,KAAAe,QAAA,CAAAC,OAAA,wCAAAC,MAAA,MAAAjC,WAAA;QACA;MACA;IACA;IAEA;IACAkC,iBAAA,WAAAA,kBAAAlB,OAAA;MACA,YAAAlB,gBAAA,CAAAqB,QAAA,CAAAH,OAAA;IACA;IAEA;IACAmB,uBAAA,WAAAA,wBAAAC,YAAA,EAAAC,gBAAA;MAAA,IAAAC,KAAA;MACA;MACA,IAAAC,WAAA,GAAAF,gBAAA,CAAAG,KAAA,WAAAxB,OAAA;QAAA,OAAAsB,KAAA,CAAAJ,iBAAA,CAAAlB,OAAA;MAAA;MAEA,IAAAuB,WAAA;QACA;QACAF,gBAAA,CAAAtB,OAAA,WAAAC,OAAA;UACA,IAAAY,KAAA,GAAAU,KAAA,CAAAxC,gBAAA,CAAA+B,OAAA,CAAAb,OAAA;UACA,IAAAY,KAAA;YACAU,KAAA,CAAAxC,gBAAA,CAAAgC,MAAA,CAAAF,KAAA;UACA;QACA;QACA,KAAAG,QAAA,CAAAU,IAAA,oCAAAR,MAAA,CAAAG,YAAA;MACA;QACA;QACAC,gBAAA,CAAAtB,OAAA,WAAAC,OAAA;UACA,KAAAsB,KAAA,CAAAJ,iBAAA,CAAAlB,OAAA,KAAAsB,KAAA,CAAAxC,gBAAA,CAAAa,MAAA,GAAA2B,KAAA,CAAAtC,WAAA;YACAsC,KAAA,CAAAxC,gBAAA,CAAAsB,IAAA,CAAAJ,OAAA;UACA;QACA;;QAEA;QACA,IAAA0B,WAAA,GAAAL,gBAAA,CAAAM,MAAA,WAAA3B,OAAA;UAAA,QAAAsB,KAAA,CAAAJ,iBAAA,CAAAlB,OAAA;QAAA;QACA,IAAA0B,WAAA,CAAA/B,MAAA;UACA,KAAAoB,QAAA,CAAAC,OAAA,wEAAAC,MAAA,CAAAG,YAAA;QACA;UACA,KAAAL,QAAA,CAAAa,OAAA,wBAAAX,MAAA,CAAAG,YAAA;QACA;MACA;IACA;IAGA;IACAS,YAAA,WAAAA,aAAA;MACA;MACA,KAAAxC,cAAA;;MAEA;MACA,UAAAT,aAAA,CAAAc,IAAA;QACA,KAAAqB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,UAAAnC,mBAAA,CAAAa,IAAA;QACA,KAAAqB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,SAAAlC,gBAAA,CAAAa,MAAA;QACA,KAAAoB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAA3B,cAAA;MACA,SAAAV,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IAEA;IACAmD,gBAAA,WAAAA,iBAAA;MACA,SAAAnD,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IAEA;IACAoD,gBAAA,WAAAA,iBAAAC,MAAA;MACA,IAAApB,KAAA,QAAA3B,mBAAA,CAAA4B,OAAA,CAAAmB,MAAA;MACA,IAAApB,KAAA;QACA,KAAA3B,mBAAA,CAAA6B,MAAA,CAAAF,KAAA;MACA;QACA,KAAA3B,mBAAA,CAAAmB,IAAA,CAAA4B,MAAA;MACA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA;MACA,KAAA9C,kBAAA;MACA,KAAAC,YAAA;IACA;IAEA;IACA8C,iBAAA,WAAAA,kBAAA;MACA,KAAA/C,kBAAA;MACA,KAAAC,YAAA;IACA;IAEA;IACA+C,gBAAA,WAAAA,iBAAA;MACA,UAAA/C,YAAA,CAAAM,IAAA;QACA,KAAAqB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,IAAAoB,UAAA;MACA,KAAAA,UAAA,CAAAC,IAAA,MAAAjD,YAAA,CAAAM,IAAA;QACA,KAAAqB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,IAAAsB,UAAA,QAAAlD,YAAA,CAAAM,IAAA;MACA,SAAAR,iBAAA,CAAAiB,QAAA,CAAAmC,UAAA;QACA,KAAAvB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAA9B,iBAAA,CAAAkB,IAAA,CAAAkC,UAAA;MACA;MACA,KAAArD,mBAAA,CAAAmB,IAAA,CAAAkC,UAAA;MAEA,KAAAvB,QAAA,CAAAa,OAAA;MACA;MACA,KAAAxC,YAAA;IACA;IAEA;IACAmD,kBAAA,WAAAA,mBAAA3B,KAAA;MACA,IAAA4B,cAAA,QAAAtD,iBAAA,CAAA0B,KAAA;MACA;MACA,KAAA1B,iBAAA,CAAA4B,MAAA,CAAAF,KAAA;MACA;MACA,IAAA6B,aAAA,QAAAxD,mBAAA,CAAA4B,OAAA,CAAA2B,cAAA;MACA,IAAAC,aAAA;QACA,KAAAxD,mBAAA,CAAA6B,MAAA,CAAA2B,aAAA;MACA;MACA,KAAA1B,QAAA,CAAAa,OAAA;IACA;IAEA;IACAc,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA/D,aAAA,CAAAc,IAAA;QACA,KAAAqB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,UAAAnC,mBAAA,CAAAa,IAAA;QACA,KAAAqB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAD,QAAA,CAAAU,IAAA;;MAEA;MACAmB,UAAA;QACA;QACA,IAAAC,cAAA,IACA,aACA,aACA,aACA,WACA,cACA,eACA,aACA,WACA,aACA,cACA,WACA,aACA,aACA,YACA;;QAEA;QACAF,MAAA,CAAA5D,iBAAA,MAAAkC,MAAA,CAAA4B,cAAA;;QAEA;QACAF,MAAA,CAAA7D,gBAAA;QACA+D,cAAA,CAAA9C,OAAA,WAAA+C,IAAA;UACA,IAAAH,MAAA,CAAA7D,gBAAA,CAAAa,MAAA,GAAAgD,MAAA,CAAA3D,WAAA;YACA2D,MAAA,CAAA7D,gBAAA,CAAAsB,IAAA,CAAA0C,IAAA;UACA;QACA;QAEAH,MAAA,CAAA5B,QAAA,CAAAa,OAAA;MACA;IACA;IAEA;IACAmB,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAAL,oBAAA;IACA;IAEA;IACAM,eAAA,WAAAA,gBAAA;MACA,KAAA1D,sBAAA;IACA;IAEA;IACA2D,oBAAA,WAAAA,qBAAA;MACA,KAAA3D,sBAAA;IACA;IAEA;IACA4D,qBAAA,WAAAA,sBAAA;MACA,KAAAnC,QAAA,CAAAU,IAAA;MACA;IACA;IAEA;IACA0B,kBAAA,WAAAA,mBAAA;MACA,KAAApC,QAAA,CAAAU,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}